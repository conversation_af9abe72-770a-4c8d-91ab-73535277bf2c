-- URGENT FIX FOR INFINITE RECURSION IN USERS TABLE POLICIES
-- Run this directly in Supabase SQL Editor to fix the issue immediately

-- First, disable <PERSON><PERSON> temporarily on users table to break the recursion
ALTER TABLE public.users DISABLE ROW LEVEL SECURITY;

-- Drop all existing problematic policies
DROP POLICY IF EXISTS "Admin users can access all users" ON public.users;
DROP POLICY IF EXISTS "Users can view their own profile" ON public.users;
DROP POLICY IF EXISTS "Users can update their own profile" ON public.users;
DROP POLICY IF EXISTS "Admin users can view all users" ON public.users;
DROP POLICY IF EXISTS "Admin users can update all users" ON public.users;
DROP POLICY IF EXISTS "School managers can view users in their tenant" ON public.users;
DROP POLICY IF EXISTS "School managers can update users in their tenant" ON public.users;

-- <PERSON><PERSON> helper functions that bypass RLS
CREATE OR REPLACE FUNCTION public.is_admin(user_id uuid DEFAULT auth.uid())
R<PERSON><PERSON>NS boolean
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  user_role text;
BEGIN
  SELECT role INTO user_role 
  FROM public.users 
  WHERE id = user_id;
  
  RETURN COALESCE(user_role = 'admin', false);
END;
$$;

CREATE OR REPLACE FUNCTION public.get_user_role(user_id uuid DEFAULT auth.uid())
RETURNS text
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  user_role text;
BEGIN
  SELECT role INTO user_role 
  FROM public.users 
  WHERE id = user_id;
  
  RETURN user_role;
END;
$$;

CREATE OR REPLACE FUNCTION public.get_user_tenant_id(user_id uuid DEFAULT auth.uid())
RETURNS uuid
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  user_tenant_id uuid;
BEGIN
  SELECT tenant_id INTO user_tenant_id 
  FROM public.users 
  WHERE id = user_id;
  
  RETURN user_tenant_id;
END;
$$;

-- Re-enable RLS
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;

-- Create new non-recursive policies
CREATE POLICY "Users can view their own profile"
ON public.users
FOR SELECT
TO authenticated
USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile"
ON public.users
FOR UPDATE
TO authenticated
USING (auth.uid() = id);

CREATE POLICY "Admin users can view all users"
ON public.users
FOR SELECT
TO authenticated
USING (public.is_admin());

CREATE POLICY "Admin users can update all users"
ON public.users
FOR UPDATE
TO authenticated
USING (public.is_admin());

CREATE POLICY "Admin users can insert users"
ON public.users
FOR INSERT
TO authenticated
WITH CHECK (public.is_admin());

CREATE POLICY "Admin users can delete users"
ON public.users
FOR DELETE
TO authenticated
USING (public.is_admin());

CREATE POLICY "School managers can view users in their tenant"
ON public.users
FOR SELECT
TO authenticated
USING (
  public.get_user_role() = 'school_manager' 
  AND tenant_id = public.get_user_tenant_id()
  AND public.get_user_tenant_id() IS NOT NULL
);

CREATE POLICY "School managers can update users in their tenant"
ON public.users
FOR UPDATE
TO authenticated
USING (
  public.get_user_role() = 'school_manager' 
  AND tenant_id = public.get_user_tenant_id()
  AND public.get_user_tenant_id() IS NOT NULL
);

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION public.is_admin(uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_role(uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_tenant_id(uuid) TO authenticated;