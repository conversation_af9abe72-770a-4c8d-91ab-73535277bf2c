# 🔍 دليل تشخيص مشكلة عدم ظهور الباص على الخريطة

## 🎯 المشكلة
**الباص موجود في قاعدة البيانات ولكن لا يظهر على الخريطة**

---

## 🔧 خطوات التشخيص

### **1. 🧪 فتح أدوات المطور (Developer Tools)**

```bash
# في المتصفح
F12 أو Ctrl+Shift+I

# انتقل إلى تبويب Console
```

### **2. 📊 فحص الرسائل التشخيصية**

بعد إضافة التسجيل المفصل، ستظهر الرسائل التالية في Console:

#### **أ. فحص Tenant ID:**
```
🚫 No tenant ID available
أو
🔍 Loading bus data for tenant: [tenant-id]
```

#### **ب. فحص البيانات من قاعدة البيانات:**
```
📊 Raw bus data from database: [array of buses]
📊 Number of buses found: [number]
```

#### **ج. فحص الاستعلام المباشر:**
```
🔍 Direct buses query result: { data: [...], error: null }
✅ Found buses in direct query: [buses array]
🚌 Processing bus: [bus object]
```

#### **د. فحص البيانات المفلترة:**
```
🔍 Total buses in busData: [number]
🔍 Filtered buses count: [number]
🔍 Filtered buses data: [array]
🔍 Current filters: [filters object]
```

#### **هـ. فحص البيانات المرسلة للخريطة:**
```
🗺️ Buses being sent to map: [array]
🗺️ Map props: { busesCount: [number], selectedBus: null, ... }
```

---

## 🔍 تحليل المشاكل المحتملة

### **❌ المشكلة 1: عدم وجود Tenant ID**
```
🚫 No tenant ID available
```
**الحل:**
- تأكد من تسجيل الدخول بشكل صحيح
- تحقق من وجود بيانات المستأجر في AuthContext

### **❌ المشكلة 2: فشل استعلام قاعدة البيانات**
```
📊 Number of buses found: 0
🔍 Direct buses query result: { data: null, error: [error] }
```
**الحل:**
- تحقق من صحة tenant_id في جدول buses
- تأكد من وجود البيانات في الجدول الصحيح
- فحص أذونات قاعدة البيانات

### **❌ المشكلة 3: مشكلة في الفلاتر**
```
🔍 Total buses in busData: 5
🔍 Filtered buses count: 0
```
**الحل:**
- مسح جميع الفلاتر
- تحقق من فلاتر الحالة والمسارات والسائقين

### **❌ المشكلة 4: مشكلة في تحويل البيانات**
```
🗺️ Buses being sent to map: []
```
**الحل:**
- تحقق من صحة تحويل البيانات
- فحص الحقول المطلوبة (latitude, longitude)

---

## 🛠️ خطوات الإصلاح

### **1. 📋 فحص بيانات الباص في قاعدة البيانات**

```sql
-- فحص الباص في جدول buses
SELECT * FROM buses WHERE tenant_id = 'your-tenant-id';

-- فحص الحقول المطلوبة
SELECT 
  id, 
  plate_number, 
  tenant_id, 
  latitude, 
  longitude, 
  is_active,
  driver_name
FROM buses 
WHERE tenant_id = 'your-tenant-id';
```

### **2. 🔧 التأكد من الحقول المطلوبة**

**الحقول الأساسية المطلوبة:**
- ✅ `id` - معرف الباص
- ✅ `plate_number` - رقم اللوحة
- ✅ `tenant_id` - معرف المستأجر
- ✅ `is_active` - حالة النشاط (true)

**الحقول الاختيارية للموقع:**
- 📍 `latitude` - خط العرض
- 📍 `longitude` - خط الطول
- 🚗 `driver_name` - اسم السائق
- 🛣️ `route_id` - معرف المسار

### **3. 🔄 مسح الفلاتر**

في صفحة التتبع:
1. انقر على "مسح الفلاتر" (Clear Filters)
2. تأكد من عدم وجود نص في البحث
3. تحقق من عدم تفعيل فلاتر الحالة

### **4. 🔍 فحص View في قاعدة البيانات**

```sql
-- فحص وجود View للحافلات مع المواقع
SELECT * FROM buses_with_latest_location 
WHERE tenant_id = 'your-tenant-id';
```

إذا لم يكن View موجود، أنشئه:

```sql
CREATE OR REPLACE VIEW buses_with_latest_location AS
SELECT 
  b.*,
  bl.latitude,
  bl.longitude,
  bl.speed,
  bl.heading,
  bl.accuracy,
  bl.timestamp as location_timestamp
FROM buses b
LEFT JOIN LATERAL (
  SELECT latitude, longitude, speed, heading, accuracy, timestamp
  FROM bus_locations 
  WHERE bus_id = b.id 
  ORDER BY timestamp DESC 
  LIMIT 1
) bl ON true;
```

---

## 🧪 اختبار سريع

### **1. إضافة باص تجريبي:**

```sql
INSERT INTO buses (
  id,
  plate_number,
  tenant_id,
  driver_name,
  capacity,
  is_active,
  latitude,
  longitude
) VALUES (
  gen_random_uuid(),
  'TEST-123',
  'your-tenant-id',
  'سائق تجريبي',
  30,
  true,
  24.7136,
  46.6753
);
```

### **2. تحديث الصفحة:**
- اضغط F5 لتحديث الصفحة
- أو انقر على زر "تحديث البيانات"

---

## 📞 إذا استمرت المشكلة

### **أرسل المعلومات التالية:**

1. **رسائل Console:** انسخ جميع الرسائل من Console
2. **بيانات الباص:** 
   ```sql
   SELECT * FROM buses WHERE plate_number = 'رقم-اللوحة';
   ```
3. **معرف المستأجر:** من AuthContext
4. **لقطة شاشة:** من صفحة التتبع

### **معلومات إضافية مفيدة:**
- نوع المتصفح والإصدار
- هل المشكلة مع باص واحد أم جميع الحافلات؟
- هل ظهر الباص من قبل ثم اختفى؟
- هل تم إضافة الباص حديثاً؟

---

## ✅ النتيجة المتوقعة

بعد تطبيق الخطوات:
- ✅ ظهور الباص على الخريطة
- ✅ إمكانية النقر عليه لعرض التفاصيل
- ✅ ظهوره في قائمة الحافلات الجانبية
- ✅ تحديث الإحصائيات بشكل صحيح

**🚀 الهدف: باص مرئي وتفاعلي على الخريطة!** ✨🎯
