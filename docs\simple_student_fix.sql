-- Simple Student Creation Fix - حل مبسط لإنشاء الطلاب
-- يتجنب استخدام pgcrypto ويستخدم Edge Function

-- 1. إنشاء دالة مبسطة لإنشاء الطالب فقط (بدون auth.users)
CREATE OR REPLACE FUNCTION create_student_simple(
  student_name text,
  student_grade text,
  student_tenant_id uuid,
  parent_id uuid DEFAULT NULL,
  route_stop_id uuid DEFAULT NULL,
  student_email text DEFAULT NULL
)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
DECLARE
  new_student_id uuid;
  result json;
BEGIN
  -- Generate new UUID for student
  new_student_id := gen_random_uuid();
  
  -- Insert into students table only
  INSERT INTO students (
    id,
    name,
    grade,
    tenant_id,
    parent_id,
    route_stop_id,
    student_id,
    is_active,
    created_at,
    updated_at
  ) VALUES (
    new_student_id,
    student_name,
    student_grade,
    student_tenant_id,
    parent_id,
    route_stop_id,
    'STU-' || EXTRACT(YEAR FROM NOW()) || '-' || LPAD((EXTRACT(EPOCH FROM NOW())::bigint % 10000)::text, 4, '0'),
    true,
    now(),
    now()
  );

  -- Return success
  result := json_build_object(
    'success', true,
    'student_id', new_student_id,
    'message', 'Student record created successfully'
  );

  RETURN result;

EXCEPTION
  WHEN OTHERS THEN
    result := json_build_object(
      'success', false,
      'error', SQLERRM,
      'message', 'Failed to create student record'
    );
    RETURN result;
END;
$$;

-- منح الصلاحيات
GRANT EXECUTE ON FUNCTION create_student_simple(text, text, uuid, uuid, uuid, text) TO authenticated;

-- 2. إنشاء دالة للحصول على الطلاب مع معلومات أولياء الأمور
CREATE OR REPLACE FUNCTION get_students_with_parents(tenant_uuid uuid)
RETURNS TABLE (
  student_id uuid,
  student_name text,
  grade text,
  student_number text,
  parent_name text,
  parent_email text,
  route_stop_name text,
  is_active boolean,
  created_at timestamptz
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    s.id as student_id,
    s.name as student_name,
    s.grade,
    s.student_id as student_number,
    COALESCE(u.name, 'غير محدد') as parent_name,
    COALESCE(u.email, 'غير محدد') as parent_email,
    COALESCE(rs.name, 'غير محدد') as route_stop_name,
    s.is_active,
    s.created_at
  FROM students s
  LEFT JOIN users u ON s.parent_id = u.id
  LEFT JOIN route_stops rs ON s.route_stop_id = rs.id
  WHERE s.tenant_id = tenant_uuid
  ORDER BY s.created_at DESC;
END;
$$;

-- منح الصلاحيات
GRANT EXECUTE ON FUNCTION get_students_with_parents(uuid) TO authenticated;

-- 3. إنشاء دالة لتحديث معلومات الطالب
CREATE OR REPLACE FUNCTION update_student_info(
  student_uuid uuid,
  new_name text DEFAULT NULL,
  new_grade text DEFAULT NULL,
  new_parent_id uuid DEFAULT NULL,
  new_route_stop_id uuid DEFAULT NULL
)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result json;
BEGIN
  -- Update student record
  UPDATE students 
  SET 
    name = COALESCE(new_name, name),
    grade = COALESCE(new_grade, grade),
    parent_id = COALESCE(new_parent_id, parent_id),
    route_stop_id = COALESCE(new_route_stop_id, route_stop_id),
    updated_at = now()
  WHERE id = student_uuid;

  -- Check if update was successful
  IF FOUND THEN
    result := json_build_object(
      'success', true,
      'message', 'Student updated successfully'
    );
  ELSE
    result := json_build_object(
      'success', false,
      'error', 'Student not found'
    );
  END IF;

  RETURN result;

EXCEPTION
  WHEN OTHERS THEN
    result := json_build_object(
      'success', false,
      'error', SQLERRM,
      'message', 'Failed to update student'
    );
    RETURN result;
END;
$$;

-- منح الصلاحيات
GRANT EXECUTE ON FUNCTION update_student_info(uuid, text, text, uuid, uuid) TO authenticated;

-- 4. التحقق من نجاح التطبيق
SELECT 
  'create_student_simple function' as component,
  CASE WHEN EXISTS (SELECT 1 FROM information_schema.routines WHERE routine_name = 'create_student_simple') 
    THEN 'Created ✅' 
    ELSE 'Not Found ❌' 
  END as status
UNION ALL
SELECT 
  'get_students_with_parents function' as component,
  CASE WHEN EXISTS (SELECT 1 FROM information_schema.routines WHERE routine_name = 'get_students_with_parents') 
    THEN 'Created ✅' 
    ELSE 'Not Found ❌' 
  END as status
UNION ALL
SELECT 
  'update_student_info function' as component,
  CASE WHEN EXISTS (SELECT 1 FROM information_schema.routines WHERE routine_name = 'update_student_info') 
    THEN 'Created ✅' 
    ELSE 'Not Found ❌' 
  END as status;
