-- دوال CRUD آمنة للمستخدمين والطلاب
-- Secure CRUD Functions for Users and Students

-- ===== دوال إدارة المستخدمين =====
-- User Management Functions

-- دالة إنشاء مستخدم جديد
CREATE OR REPLACE FUNCTION create_user_account(
  user_email text,
  user_password text,
  user_name text,
  user_role user_role,
  user_tenant_id uuid,
  user_phone text DEFAULT NULL
)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  new_user_id uuid;
  result json;
  current_user_role text;
  current_tenant_id uuid;
BEGIN
  -- التحقق من الصلاحيات
  SELECT role::text, tenant_id INTO current_user_role, current_tenant_id 
  FROM users WHERE id = auth.uid();
  
  -- التحقق من أن المستخدم الحالي يمكنه إنشاء مستخدمين
  IF NOT (
    current_user_role = 'admin' OR
    (current_user_role = 'school_manager' AND current_tenant_id = user_tenant_id AND user_role != 'admin')
  ) THEN
    RETURN json_build_object(
      'success', false,
      'error', 'PERMISSION_DENIED',
      'message', 'You do not have permission to create users'
    );
  END IF;

  -- للأدمن: إذا لم يتم تحديد tenant_id، استخدم أول tenant متاح
  IF current_user_role = 'admin' AND user_tenant_id IS NULL THEN
    SELECT id INTO user_tenant_id FROM tenants WHERE is_active = true LIMIT 1;

    IF user_tenant_id IS NULL THEN
      RETURN json_build_object(
        'success', false,
        'error', 'NO_TENANT_AVAILABLE',
        'message', 'No active tenant found. Please create a school first.'
      );
    END IF;
  END IF;

  -- التحقق من صحة البيانات
  IF user_email IS NULL OR user_email = '' THEN
    RETURN json_build_object(
      'success', false,
      'error', 'VALIDATION_ERROR',
      'message', 'Email is required'
    );
  END IF;

  IF user_name IS NULL OR user_name = '' THEN
    RETURN json_build_object(
      'success', false,
      'error', 'VALIDATION_ERROR',
      'message', 'Name is required'
    );
  END IF;

  -- التحقق من عدم وجود مستخدم بنفس البريد الإلكتروني
  IF EXISTS (SELECT 1 FROM users WHERE email = user_email) THEN
    RETURN json_build_object(
      'success', false,
      'error', 'EMAIL_EXISTS',
      'message', 'A user with this email already exists'
    );
  END IF;

  -- إنشاء معرف جديد للمستخدم
  new_user_id := gen_random_uuid();
  
  -- إدراج سجل المستخدم
  INSERT INTO users (
    id,
    email,
    name,
    role,
    tenant_id,
    phone,
    is_active,
    created_at,
    updated_at
  ) VALUES (
    new_user_id,
    user_email,
    user_name,
    user_role,
    user_tenant_id,
    user_phone,
    true,
    now(),
    now()
  );
  
  -- إرجاع نتيجة النجاح
  RETURN json_build_object(
    'success', true,
    'user_id', new_user_id,
    'message', 'User created successfully',
    'data', json_build_object(
      'id', new_user_id,
      'email', user_email,
      'name', user_name,
      'role', user_role,
      'tenant_id', user_tenant_id
    )
  );
  
EXCEPTION
  WHEN OTHERS THEN
    RETURN json_build_object(
      'success', false,
      'error', 'DATABASE_ERROR',
      'message', 'Failed to create user: ' || SQLERRM
    );
END;
$$;

-- دالة تحديث مستخدم
CREATE OR REPLACE FUNCTION update_user_account(
  target_user_id uuid,
  user_name text DEFAULT NULL,
  user_phone text DEFAULT NULL,
  user_is_active boolean DEFAULT NULL
)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result json;
  current_user_role text;
  current_tenant_id uuid;
  target_user_tenant_id uuid;
  target_user_role text;
BEGIN
  -- الحصول على معلومات المستخدم الحالي
  SELECT role::text, tenant_id INTO current_user_role, current_tenant_id 
  FROM users WHERE id = auth.uid();
  
  -- الحصول على معلومات المستخدم المستهدف
  SELECT tenant_id, role::text INTO target_user_tenant_id, target_user_role
  FROM users WHERE id = target_user_id;
  
  IF NOT FOUND THEN
    RETURN json_build_object(
      'success', false,
      'error', 'USER_NOT_FOUND',
      'message', 'User not found'
    );
  END IF;
  
  -- التحقق من الصلاحيات
  IF NOT (
    current_user_role = 'admin' OR 
    (current_user_role = 'school_manager' AND current_tenant_id = target_user_tenant_id AND target_user_role != 'admin') OR
    (auth.uid() = target_user_id) -- المستخدم يحدث ملفه الشخصي
  ) THEN
    RETURN json_build_object(
      'success', false,
      'error', 'PERMISSION_DENIED',
      'message', 'You do not have permission to update this user'
    );
  END IF;

  -- تحديث البيانات
  UPDATE users SET
    name = COALESCE(user_name, name),
    phone = COALESCE(user_phone, phone),
    is_active = COALESCE(user_is_active, is_active),
    updated_at = now()
  WHERE id = target_user_id;
  
  RETURN json_build_object(
    'success', true,
    'message', 'User updated successfully'
  );
  
EXCEPTION
  WHEN OTHERS THEN
    RETURN json_build_object(
      'success', false,
      'error', 'DATABASE_ERROR',
      'message', 'Failed to update user: ' || SQLERRM
    );
END;
$$;

-- دالة حذف مستخدم
CREATE OR REPLACE FUNCTION delete_user_account(target_user_id uuid)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result json;
  current_user_role text;
  current_tenant_id uuid;
  target_user_tenant_id uuid;
  target_user_role text;
BEGIN
  -- الحصول على معلومات المستخدم الحالي
  SELECT role::text, tenant_id INTO current_user_role, current_tenant_id 
  FROM users WHERE id = auth.uid();
  
  -- الحصول على معلومات المستخدم المستهدف
  SELECT tenant_id, role::text INTO target_user_tenant_id, target_user_role
  FROM users WHERE id = target_user_id;
  
  IF NOT FOUND THEN
    RETURN json_build_object(
      'success', false,
      'error', 'USER_NOT_FOUND',
      'message', 'User not found'
    );
  END IF;
  
  -- التحقق من الصلاحيات
  IF NOT (
    current_user_role = 'admin' OR 
    (current_user_role = 'school_manager' AND current_tenant_id = target_user_tenant_id AND target_user_role != 'admin')
  ) THEN
    RETURN json_build_object(
      'success', false,
      'error', 'PERMISSION_DENIED',
      'message', 'You do not have permission to delete this user'
    );
  END IF;

  -- منع حذف المستخدم الحالي
  IF target_user_id = auth.uid() THEN
    RETURN json_build_object(
      'success', false,
      'error', 'CANNOT_DELETE_SELF',
      'message', 'You cannot delete your own account'
    );
  END IF;

  -- حذف المستخدم (soft delete)
  UPDATE users SET
    is_active = false,
    updated_at = now()
  WHERE id = target_user_id;
  
  RETURN json_build_object(
    'success', true,
    'message', 'User deleted successfully'
  );
  
EXCEPTION
  WHEN OTHERS THEN
    RETURN json_build_object(
      'success', false,
      'error', 'DATABASE_ERROR',
      'message', 'Failed to delete user: ' || SQLERRM
    );
END;
$$;

-- ===== دوال إدارة الطلاب =====
-- Student Management Functions

-- دالة إنشاء طالب جديد مع حساب مستخدم
CREATE OR REPLACE FUNCTION create_student_account(
  student_email text,
  student_password text,
  student_name text,
  student_grade text,
  student_tenant_id uuid,
  student_parent_id uuid DEFAULT NULL,
  student_route_stop_id uuid DEFAULT NULL
)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  new_user_id uuid;
  result json;
  current_user_role text;
  current_tenant_id uuid;
BEGIN
  -- التحقق من الصلاحيات
  SELECT role::text, tenant_id INTO current_user_role, current_tenant_id 
  FROM users WHERE id = auth.uid();
  
  IF NOT (
    current_user_role = 'admin' OR 
    (current_user_role IN ('school_manager', 'supervisor') AND current_tenant_id = student_tenant_id)
  ) THEN
    RETURN json_build_object(
      'success', false,
      'error', 'PERMISSION_DENIED',
      'message', 'You do not have permission to create students'
    );
  END IF;

  -- التحقق من صحة البيانات
  IF student_email IS NULL OR student_email = '' THEN
    RETURN json_build_object(
      'success', false,
      'error', 'VALIDATION_ERROR',
      'message', 'Email is required'
    );
  END IF;

  IF student_name IS NULL OR student_name = '' THEN
    RETURN json_build_object(
      'success', false,
      'error', 'VALIDATION_ERROR',
      'message', 'Name is required'
    );
  END IF;

  IF student_tenant_id IS NULL THEN
    RETURN json_build_object(
      'success', false,
      'error', 'VALIDATION_ERROR',
      'message', 'School is required'
    );
  END IF;

  -- التحقق من عدم وجود مستخدم بنفس البريد الإلكتروني
  IF EXISTS (SELECT 1 FROM users WHERE email = student_email) THEN
    RETURN json_build_object(
      'success', false,
      'error', 'EMAIL_EXISTS',
      'message', 'A user with this email already exists'
    );
  END IF;

  -- إنشاء معرف جديد للمستخدم
  new_user_id := gen_random_uuid();
  
  -- إدراج سجل المستخدم
  INSERT INTO users (
    id,
    email,
    name,
    role,
    tenant_id,
    is_active,
    created_at,
    updated_at
  ) VALUES (
    new_user_id,
    student_email,
    student_name,
    'student',
    student_tenant_id,
    true,
    now(),
    now()
  );
  
  -- إدراج سجل الطالب
  INSERT INTO students (
    id,
    name,
    grade,
    tenant_id,
    parent_id,
    route_stop_id,
    is_active,
    created_at,
    updated_at
  ) VALUES (
    new_user_id,
    student_name,
    student_grade,
    student_tenant_id,
    student_parent_id,
    student_route_stop_id,
    true,
    now(),
    now()
  );
  
  RETURN json_build_object(
    'success', true,
    'user_id', new_user_id,
    'message', 'Student created successfully',
    'data', json_build_object(
      'id', new_user_id,
      'email', student_email,
      'name', student_name,
      'grade', student_grade,
      'tenant_id', student_tenant_id
    )
  );
  
EXCEPTION
  WHEN OTHERS THEN
    RETURN json_build_object(
      'success', false,
      'error', 'DATABASE_ERROR',
      'message', 'Failed to create student: ' || SQLERRM
    );
END;
$$;

-- منح الصلاحيات للدوال
GRANT EXECUTE ON FUNCTION create_user_account(text, text, text, user_role, uuid, text) TO authenticated;
GRANT EXECUTE ON FUNCTION update_user_account(uuid, text, text, boolean) TO authenticated;
GRANT EXECUTE ON FUNCTION delete_user_account(uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION create_student_account(text, text, text, text, uuid, uuid, uuid) TO authenticated;
