-- ===================================================================
-- المرحلة الأولى: تطبيق سياسات RLS الجديدة والموحدة
-- Phase 1: New Unified RLS Policies Implementation
-- Generated: 2025-01-30
-- ===================================================================

-- ===== 1. إنشاء دالة التحقق من الصلاحيات المركزية =====
-- Create centralized permission check function

CREATE OR REPLACE FUNCTION public.check_permission(
  user_id uuid,
  resource_type text,
  action text,
  resource_tenant_id uuid DEFAULT null,
  resource_owner_id uuid DEFAULT null,
  additional_context jsonb DEFAULT '{}'::jsonb
)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  user_role text;
  user_tenant_id uuid;
  permission_scope text;
  permission_conditions jsonb;
  has_permission boolean := false;
BEGIN
  -- الحصول على معلومات المستخدم
  user_role := public.get_user_role_secure(user_id);
  user_tenant_id := public.get_user_tenant_secure(user_id);
  
  -- البحث عن الصلاحية في المصفوفة
  SELECT scope, conditions INTO permission_scope, permission_conditions
  FROM public.permission_matrix
  WHERE role = user_role 
    AND resource_type = check_permission.resource_type 
    AND action = check_permission.action 
    AND is_active = true
  LIMIT 1;
  
  -- إذا لم توجد صلاحية، الرفض
  IF permission_scope IS NULL THEN
    RETURN false;
  END IF;
  
  -- التحقق حسب نطاق الصلاحية
  CASE permission_scope
    WHEN 'global' THEN
      has_permission := true;
      
    WHEN 'tenant' THEN
      has_permission := (user_tenant_id = resource_tenant_id);
      
    WHEN 'own' THEN
      has_permission := (user_id = resource_owner_id);
      
    WHEN 'assigned' THEN
      -- للسائقين: التحقق من تخصيص الحافلة
      IF user_role = 'driver' AND resource_type = 'bus' THEN
        has_permission := EXISTS (
          SELECT 1 FROM public.buses 
          WHERE id = (additional_context->>'resource_id')::uuid 
            AND driver_id = user_id
        );
      ELSIF user_role = 'driver' AND resource_type = 'route' THEN
        has_permission := EXISTS (
          SELECT 1 FROM public.routes r
          JOIN public.buses b ON r.bus_id = b.id
          WHERE r.id = (additional_context->>'resource_id')::uuid 
            AND b.driver_id = user_id
        );
      END IF;
      
    WHEN 'children' THEN
      -- لأولياء الأمور: التحقق من الأطفال
      IF user_role = 'parent' AND resource_type = 'student' THEN
        has_permission := EXISTS (
          SELECT 1 FROM public.students 
          WHERE id = (additional_context->>'resource_id')::uuid 
            AND parent_id = user_id
        );
      ELSIF user_role = 'parent' AND resource_type = 'attendance' THEN
        has_permission := EXISTS (
          SELECT 1 FROM public.attendance a
          JOIN public.students s ON a.student_id = s.id
          WHERE a.id = (additional_context->>'resource_id')::uuid 
            AND s.parent_id = user_id
        );
      END IF;
      
    WHEN 'route' THEN
      -- للسائقين: الوصول لطلاب المسار المخصص
      IF user_role = 'driver' AND resource_type = 'student' THEN
        has_permission := EXISTS (
          SELECT 1 FROM public.students s
          JOIN public.routes r ON s.route_stop_id IN (
            SELECT id FROM public.route_stops WHERE route_id = r.id
          )
          JOIN public.buses b ON r.bus_id = b.id
          WHERE s.id = (additional_context->>'resource_id')::uuid 
            AND b.driver_id = user_id
        );
      END IF;
      
    ELSE
      has_permission := false;
  END CASE;
  
  -- التحقق من الشروط الإضافية
  IF has_permission AND permission_conditions IS NOT NULL AND permission_conditions != '{}'::jsonb THEN
    -- مثال: منع مدير المدرسة من تعديل الأدمن
    IF permission_conditions ? 'exclude_roles' THEN
      IF user_role = 'school_manager' AND resource_type = 'user' THEN
        has_permission := NOT EXISTS (
          SELECT 1 FROM public.users 
          WHERE id = (additional_context->>'resource_id')::uuid 
            AND role = ANY(
              SELECT jsonb_array_elements_text(permission_conditions->'exclude_roles')
            )
        );
      END IF;
    END IF;
  END IF;
  
  -- تسجيل محاولة الوصول
  IF NOT has_permission THEN
    PERFORM public.log_security_event(
      'PERMISSION_DENIED',
      'WARNING',
      format('Permission denied: %s cannot %s %s', user_role, action, resource_type),
      user_id,
      user_tenant_id,
      jsonb_build_object(
        'resource_type', resource_type,
        'action', action,
        'scope', permission_scope,
        'resource_tenant_id', resource_tenant_id,
        'resource_owner_id', resource_owner_id
      )
    );
  END IF;
  
  RETURN has_permission;
EXCEPTION
  WHEN OTHERS THEN
    -- تسجيل الخطأ
    PERFORM public.log_security_event(
      'PERMISSION_CHECK_ERROR',
      'ERROR',
      format('Error checking permission: %s', SQLERRM),
      user_id,
      user_tenant_id,
      jsonb_build_object(
        'resource_type', resource_type,
        'action', action,
        'error', SQLERRM
      )
    );
    RETURN false;
END;
$$;

-- منح الصلاحية للدالة
GRANT EXECUTE ON FUNCTION public.check_permission(uuid, text, text, uuid, uuid, jsonb) TO authenticated;

-- ===== 2. تفعيل RLS على الجداول الرئيسية =====
-- Enable RLS on main tables

ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tenants ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.buses ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.routes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.students ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.attendance ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.bus_maintenance ENABLE ROW LEVEL SECURITY;

-- ===== 3. إنشاء السياسات الموحدة لجدول المستخدمين =====
-- Create unified policies for users table

-- سياسة القراءة للمستخدمين
CREATE POLICY "users_read_policy" ON public.users
FOR SELECT TO authenticated
USING (
  public.check_permission(
    auth.uid(),
    'user',
    'read',
    tenant_id,
    id,
    jsonb_build_object('resource_id', id)
  )
);

-- سياسة التحديث للمستخدمين
CREATE POLICY "users_update_policy" ON public.users
FOR UPDATE TO authenticated
USING (
  public.check_permission(
    auth.uid(),
    'user',
    'update',
    tenant_id,
    id,
    jsonb_build_object('resource_id', id)
  )
)
WITH CHECK (
  public.check_permission(
    auth.uid(),
    'user',
    'update',
    tenant_id,
    id,
    jsonb_build_object('resource_id', id)
  )
);

-- سياسة الإنشاء للمستخدمين
CREATE POLICY "users_create_policy" ON public.users
FOR INSERT TO authenticated
WITH CHECK (
  public.check_permission(
    auth.uid(),
    'user',
    'create',
    tenant_id,
    null,
    jsonb_build_object('target_tenant_id', tenant_id)
  )
);

-- سياسة الحذف للمستخدمين
CREATE POLICY "users_delete_policy" ON public.users
FOR DELETE TO authenticated
USING (
  public.check_permission(
    auth.uid(),
    'user',
    'delete',
    tenant_id,
    id,
    jsonb_build_object('resource_id', id)
  )
);

-- ===== 4. إنشاء السياسات الموحدة لجدول المدارس =====
-- Create unified policies for tenants table

-- سياسة القراءة للمدارس
CREATE POLICY "tenants_read_policy" ON public.tenants
FOR SELECT TO authenticated
USING (
  public.check_permission(
    auth.uid(),
    'tenant',
    'read',
    id,
    null,
    jsonb_build_object('resource_id', id)
  )
);

-- سياسة التحديث للمدارس
CREATE POLICY "tenants_update_policy" ON public.tenants
FOR UPDATE TO authenticated
USING (
  public.check_permission(
    auth.uid(),
    'tenant',
    'update',
    id,
    null,
    jsonb_build_object('resource_id', id)
  )
)
WITH CHECK (
  public.check_permission(
    auth.uid(),
    'tenant',
    'update',
    id,
    null,
    jsonb_build_object('resource_id', id)
  )
);

-- سياسة الإنشاء للمدارس
CREATE POLICY "tenants_create_policy" ON public.tenants
FOR INSERT TO authenticated
WITH CHECK (
  public.check_permission(
    auth.uid(),
    'tenant',
    'create',
    null,
    null,
    '{}'::jsonb
  )
);

-- سياسة الحذف للمدارس
CREATE POLICY "tenants_delete_policy" ON public.tenants
FOR DELETE TO authenticated
USING (
  public.check_permission(
    auth.uid(),
    'tenant',
    'delete',
    id,
    null,
    jsonb_build_object('resource_id', id)
  )
);

-- تسجيل إتمام تطبيق السياسات الجديدة
SELECT public.log_security_event(
  'NEW_RLS_POLICIES_APPLIED',
  'INFO',
  'Phase 1 new unified RLS policies applied successfully',
  auth.uid(),
  null,
  jsonb_build_object(
    'phase', 1,
    'action', 'rls_policies',
    'tables_secured', 8,
    'policies_created', 12
  )
);
