import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers":
    "authorization, x-client-info, apikey, content-type",
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    // Create Supabase admin client
    const supabaseUrl = Deno.env.get("SUPABASE_URL");
    const supabaseServiceKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY");

    console.log("Environment check:", {
      SUPABASE_URL: !!supabaseUrl,
      SUPABASE_SERVICE_ROLE_KEY: !!supabaseServiceKey,
      url: supabaseUrl ? supabaseUrl.substring(0, 20) + "..." : "missing",
    });

    if (!supabaseUrl || !supabaseServiceKey) {
      console.error("Missing environment variables:", {
        SUPABASE_URL: !!supabaseUrl,
        SUPABASE_SERVICE_ROLE_KEY: !!supabaseServiceKey,
      });
      return new Response(
        JSON.stringify({
          success: false,
          error: "Server configuration error - missing environment variables",
        }),
        {
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        },
      );
    }

    const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
      },
    });

    let requestBody;
    try {
      requestBody = await req.json();
      console.log("Request body received:", {
        email: !!requestBody.email,
        password: !!requestBody.password,
        name: !!requestBody.name,
        role: requestBody.role,
        tenant_id: requestBody.tenant_id,
        phone: !!requestBody.phone,
        is_active: requestBody.is_active,
      });
    } catch (jsonError) {
      console.error("Failed to parse request body:", jsonError);
      return new Response(
        JSON.stringify({
          success: false,
          error: "Invalid JSON in request body",
        }),
        {
          status: 400,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        },
      );
    }

    const { email, password, name, role, tenant_id, phone, is_active, student_data } =
      requestBody;

    // Validate required fields
    if (!email || !password || !name || !role) {
      const errorDetails = {
        email: !!email,
        password: !!password,
        name: !!name,
        role: !!role,
        tenant_id: !!tenant_id,
        receivedData: { email, password: !!password, name, role, tenant_id },
      };
      console.error("Missing required fields:", errorDetails);
      return new Response(
        JSON.stringify({
          success: false,
          error:
            "Missing required fields: " +
            [
              !email && "email",
              !password && "password",
              !name && "name",
              !role && "role",
            ]
              .filter(Boolean)
              .join(", "),
          details: errorDetails,
        }),
        {
          status: 400,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        },
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      console.error("Invalid email format:", email);
      return new Response(
        JSON.stringify({
          success: false,
          error: "Invalid email format",
        }),
        {
          status: 400,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        },
      );
    }

    // Validate password length
    if (password.length < 6) {
      console.error("Password too short:", password.length);
      return new Response(
        JSON.stringify({
          success: false,
          error: "Password must be at least 6 characters long",
        }),
        {
          status: 400,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        },
      );
    }

    // Create auth user
    console.log("Creating auth user with email:", email);
    const { data: authData, error: authError } =
      await supabaseAdmin.auth.admin.createUser({
        email,
        password,
        email_confirm: true,
        user_metadata: {
          name,
          role,
        },
      });

    if (authError) {
      console.error("Auth error:", authError);
      return new Response(
        JSON.stringify({
          success: false,
          error: `Authentication error: ${authError.message}`,
        }),
        {
          status: 400,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        },
      );
    }

    if (!authData.user) {
      return new Response(
        JSON.stringify({
          success: false,
          error: "Failed to create user",
        }),
        {
          status: 400,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        },
      );
    }

    // Create user profile
    console.log("Creating user profile for user ID:", authData.user.id);
    const profileData = {
      id: authData.user.id,
      email,
      name,
      role,
      tenant_id,
      phone,
      is_active: is_active ?? true,
      metadata: {},
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
    console.log("Profile data to insert:", profileData);

    const { error: profileError } = await supabaseAdmin
      .from("users")
      .insert(profileData);

    if (profileError) {
      console.error("Profile error:", profileError);
      // Try to clean up the auth user if profile creation failed
      console.log("Cleaning up auth user due to profile error");
      await supabaseAdmin.auth.admin.deleteUser(authData.user.id);

      return new Response(
        JSON.stringify({
          success: false,
          error: `Profile creation error: ${profileError.message}`,
        }),
        {
          status: 400,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        },
      );
    }

    // If role is student, create student record
    let studentId = null;
    if (role === 'student' && student_data) {
      console.log("Creating student record for user ID:", authData.user.id);
      const studentRecord = {
        id: crypto.randomUUID(),
        name,
        grade: student_data.grade,
        tenant_id,
        parent_id: student_data.parent_id,
        route_stop_id: student_data.route_stop_id,
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      const { data: studentData, error: studentError } = await supabaseAdmin
        .from("students")
        .insert(studentRecord)
        .select()
        .single();

      if (studentError) {
        console.error("Student creation error:", studentError);
        // Clean up user and profile if student creation failed
        await supabaseAdmin.auth.admin.deleteUser(authData.user.id);
        await supabaseAdmin.from("users").delete().eq("id", authData.user.id);

        return new Response(
          JSON.stringify({
            success: false,
            error: `Student creation error: ${studentError.message}`,
          }),
          {
            status: 400,
            headers: { ...corsHeaders, "Content-Type": "application/json" },
          },
        );
      }

      studentId = studentData.id;
      console.log("Student record created with ID:", studentId);
    }

    return new Response(
      JSON.stringify({
        success: true,
        user: {
          id: authData.user.id,
          email,
          name,
          role,
          tenant_id,
          phone,
          is_active: is_active ?? true,
        },
        student_id: studentId,
        message: role === 'student' ? 'Student created successfully' : 'User created successfully',
      }),
      {
        status: 200,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      },
    );
  } catch (error) {
    console.error("Unexpected error:", error);
    return new Response(
      JSON.stringify({
        success: false,
        error: "Internal server error",
        details: error.message,
      }),
      {
        status: 500,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      },
    );
  }
});
