/**
 * نظام توجيه التطبيقات المتعددة
 * Multi-App Router System
 * 
 * يوجه المستخدمين إلى التطبيق المناسب حسب دورهم
 * Routes users to appropriate app based on their role
 */

import React, { useEffect, useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { UserRole } from '../types';
import { LoadingSpinner } from '../components/ui/LoadingSpinner';

// تطبيقات مختلفة حسب الدور
import AdminApp from './admin/AdminApp';
import SchoolManagerApp from './school-manager/SchoolManagerApp';
import SupervisorApp from './supervisor/SupervisorApp';
import DriverApp from './driver/DriverApp';
import ParentApp from './parent/ParentApp';
import StudentApp from './student/StudentApp';

// صفحة اختيار التطبيق للأدوار المتعددة
import AppSelector from './AppSelector';

interface AppRouterProps {
  defaultApp?: string;
}

export const AppRouter: React.FC<AppRouterProps> = ({ defaultApp }) => {
  const { user, isLoading } = useAuth();
  const [selectedApp, setSelectedApp] = useState<string | null>(defaultApp || null);
  const [availableApps, setAvailableApps] = useState<string[]>([]);

  useEffect(() => {
    if (user) {
      // تحديد التطبيقات المتاحة حسب الدور
      const apps = getAvailableApps(user.role);
      setAvailableApps(apps);

      // إذا كان هناك تطبيق واحد فقط، اختره تلقائياً
      if (apps.length === 1) {
        setSelectedApp(apps[0]);
      } else if (defaultApp && apps.includes(defaultApp)) {
        setSelectedApp(defaultApp);
      }
    }
  }, [user, defaultApp]);

  // تحديد التطبيقات المتاحة حسب الدور
  const getAvailableApps = (role: UserRole): string[] => {
    switch (role) {
      case UserRole.ADMIN:
        return ['admin', 'school-manager', 'supervisor'];
      case UserRole.SCHOOL_MANAGER:
        return ['school-manager', 'supervisor'];
      case UserRole.SUPERVISOR:
        return ['supervisor'];
      case UserRole.DRIVER:
        return ['driver'];
      case UserRole.PARENT:
        return ['parent'];
      case UserRole.STUDENT:
        return ['student'];
      default:
        return [];
    }
  };

  // رندر التطبيق المحدد
  const renderSelectedApp = () => {
    if (!selectedApp || !user) return null;

    switch (selectedApp) {
      case 'admin':
        return <AdminApp user={user} />;
      case 'school-manager':
        return <SchoolManagerApp user={user} />;
      case 'supervisor':
        return <SupervisorApp user={user} />;
      case 'driver':
        return <DriverApp user={user} />;
      case 'parent':
        return <ParentApp user={user} />;
      case 'student':
        return <StudentApp user={user} />;
      default:
        return <div className="p-4 text-center text-red-600">تطبيق غير معروف</div>;
    }
  };

  // حالة التحميل
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  // المستخدم غير مسجل دخول
  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            يرجى تسجيل الدخول
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            يجب تسجيل الدخول للوصول إلى التطبيق
          </p>
        </div>
      </div>
    );
  }

  // لا توجد تطبيقات متاحة
  if (availableApps.length === 0) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            لا توجد تطبيقات متاحة
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            لا يوجد تطبيقات متاحة لدورك الحالي
          </p>
        </div>
      </div>
    );
  }

  // عرض اختيار التطبيق إذا كان هناك أكثر من تطبيق ولم يتم اختيار أي منها
  if (availableApps.length > 1 && !selectedApp) {
    return (
      <AppSelector
        availableApps={availableApps}
        userRole={user.role}
        onAppSelect={setSelectedApp}
      />
    );
  }

  // رندر التطبيق المحدد
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* شريط تبديل التطبيقات */}
      {availableApps.length > 1 && (
        <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-12">
              <div className="flex items-center space-x-4 space-x-reverse">
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  التطبيق الحالي:
                </span>
                <select
                  value={selectedApp || ''}
                  onChange={(e) => setSelectedApp(e.target.value)}
                  className="text-sm border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                >
                  {availableApps.map((app) => (
                    <option key={app} value={app}>
                      {getAppDisplayName(app)}
                    </option>
                  ))}
                </select>
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                مرحباً، {user.name}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* التطبيق المحدد */}
      {renderSelectedApp()}
    </div>
  );
};

// الحصول على اسم التطبيق للعرض
const getAppDisplayName = (app: string): string => {
  const names: Record<string, string> = {
    'admin': 'لوحة الإدارة',
    'school-manager': 'إدارة المدرسة',
    'supervisor': 'الإشراف',
    'driver': 'السائق',
    'parent': 'ولي الأمر',
    'student': 'الطالب'
  };
  return names[app] || app;
};

export default AppRouter;
