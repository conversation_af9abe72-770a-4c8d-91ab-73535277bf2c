✅ الخطة الشاملة لإعادة هيكلة نظام الصلاحيات والأمان في نظام إدارة الحافلات المدرسية (مع مراحل الدخول والأمان)
🔐 🟠 المرحلة 1: بناء نظام دخول محمي وسياسات أمان قوية
✅ مكونات نظام الدخول:
نموذج تسجيل دخول آمن:

دعم البريد الإلكتروني + كلمة المرور

تحقق من الإدخالات + قيود على عدد المحاولات

حماية من brute-force + CSRF

نظام التحقق الثنائي 2FA:

عبر البريد الإلكتروني / SMS / Google Authenticator

إلزامي لأدوار (مدير / أدمن)

إعداد مرن حسب الدور

تنفيذ تسجيل دخول باستخدام JWT أو Laravel Sanctum:

توكنات قصيرة العمر + آلية refresh

دعم تعدد الجلسات

زر "تسجيل الخروج من كل الأجهزة"

سجل الدخول:

حفظ المحاولات الناجحة والفاشلة (IP + جهاز + وقت)

لوحة مشاهدة للأدمن

✅ سياسات أمان متقدمة:
قيود كلمات المرور:

≥ 8 حروف، تحتوي على حرف كبير، صغير، رقم، ورمز

منع كلمات المرور الضعيفة

سياسات الجلسة:

انتهاء تلقائي بعد 30 دقيقة خمول

إشعار المستخدم عند الانتهاء

منع الدخول المتزامن من مواقع مختلفة (اختياري)

صلاحيات API:

حماية كل المسارات بـ middlewares

منع الوصول بدون توكن صالح

منع الاستدعاء غير المصرّح به حسب الدور

مراقبة العمليات (Audit Logging):

تسجيل كل عملية (CRUD)

يشمل: المستخدم + نوع العملية + قبل/بعد + وقت التنفيذ

🧹 🟠 المرحلة 2: تنظيف شامل وتحضير النظام
نسخ احتياطي شامل للبيانات

حذف RLS القديم من جميع الجداول

إزالة الدوال والمكونات القديمة المتعلقة بالصلاحيات

تنظيف الواجهة والكود من التكرار

توثيق كل خطوة في changelog

📋 🟠 المرحلة 3: تحليل شامل لكل مكونات النظام
✅ مراجعة الصفحات والواجهات:
تحليل صفحات: Schools, Users, Students, Routes, Buses, Notifications, Reports, Themes, Profile, Settings, إلخ.

التأكد من:

توفر عمليات CRUD

التعامل الصحيح مع الصلاحيات

الدعم الكامل للترجمة

الحماية من أخطاء البيانات

🧱 🟠 المرحلة 4: إعادة بناء نظام الصلاحيات RLS + Frontend
✅ Backend (PostgreSQL + Laravel):
إنشاء RLS حديث لكل جدول:

تصفية حسب tenant_id, role, user_id

منع تجاوز الصلاحيات تمامًا

تطوير سياسات صلاحيات منفصلة لكل جدول

حماية إضافية للـ API routes بـ middleware خاص بالدور

✅ Frontend:
إنشاء usePermissions() hook

حماية الصفحات والعناصر بـ PermissionGuard, RoleBasedAccess

تطوير نظام مرن لإدارة الصلاحيات من لوحة الأدمن

🧑‍🤝‍🧑 🟠 المرحلة 5: تصميم هيكل الأدوار والصلاحيات
الدور	الصلاحيات
أدمن	وصول شامل لجميع المدارس والبيانات، إدارة النظام بالكامل
مدير مدرسة	CRUD على مدرسته، إحصائيات وتقارير خاصة بها
مشرف	مشاهدة فقط داخل نطاق مدرسته
سائق	وصول لمساره وحافلته فقط
ولي أمر	مشاهدة بيانات أبنائه فقط

يتم إعطاء كل صلاحية يدويًا أو تلقائيًا حسب الدور.

لا توجد صلاحيات افتراضية بدون تصريح.

📊 🟠 المرحلة 6: تطوير نظام إحصائيات احترافي
✅ للأدمن:
مدارس نشطة / غير نشطة

تغطية المسارات

الحضور والغياب الإجمالي

الحافلات قيد التشغيل / الصيانة

✅ لمدير المدرسة:
عدد الطلاب، نسب الحضور والغياب

حالة الحافلات داخل مدرسته

تغطية خطوط السير

تتبع حي مباشر للحافلات الخاصة به فقط

دعم API خاصة للإحصائيات

دعم التحديث المباشر عبر WebSocket أو Redis

🌐 🟠 المرحلة 7: نظام الترجمة متعدد اللغات
دعم كامل لـ i18n (عربي/إنجليزي)

استخدام مفاتيح ترجمة موحدة

تبديل تلقائي RTL / LTR حسب اللغة

اختبار جميع الصفحات للتوافق اللغوي

🛠️ 🟠 المرحلة 8: اختبارات وضمان جودة
اختبار صلاحيات كل دور بشكل منفصل

اختبار سيناريوهات الدخول والخروج

اختبار اختراق الصلاحيات (Security Test)

اختبار الأداء وقياس سرعة النظام

اختبار على مختلف الأجهزة والمتصفحات

Unit + Integration Tests

🔄 🟢 المرحلة 9: تحسينات اختيارية وميزات إضافية
زر “View As” للأدمن لتجربة الواجهات حسب الأدوار

نظام تنبيهات ديناميكي (Notifications by Role)

تدفق مرئي لرحلة المستخدم (Flow Visualization)

إعدادات تسجيل دخول مرنة (مدة الجلسة، محاولات خاطئة، إلخ)

🗂️ الملفات النهائية المطلوبة:
📄 توثيق RLS + صلاحيات + API Policies

📊 ملف إحصائيات JSON/Excel

📁 changelog + audit logs

✅ نسخة تجريبية كاملة للواجهة + لوحة الصلاحيات

💬 ملفات ترجمة كاملة en/ar

🔁 🟢 المرحلة العاشرة: تحسينات احترافية اختيارية (Advanced Enhancements)
تهدف هذه المرحلة إلى رفع مستوى الاحترافية، الأمان، وسهولة الإدارة في النظام بعد تطبيق المراحل الأساسية.

✅ 1. محرر بصري للصلاحيات (Permissions Matrix Editor)
واجهة تفاعلية تُمكّن الأدمن من تخصيص صلاحيات الأدوار بسهولة

مصفوفة (دور × جدول × إجراء CRUD) قابلة للتعديل من الواجهة

إمكانية حفظ وتطبيق قوالب جاهزة للصلاحيات

✅ 2. كشف السلوكيات الغريبة (Anomaly Detection)
نظام آلي يرصد:

دخول من موقع جغرافي مختلف فجأة

محاولات دخول كثيرة خلال وقت قصير

دخول في أوقات غير منطقية

يتخذ إجراء تلقائي مثل: إشعار الأدمن، تعليق الجلسة، طلب 2FA إضافي

✅ 3. دعم تسجيل الدخول الموحد (OAuth / SSO)
تمكين تسجيل الدخول عبر:

Google

Microsoft

مزودي OAuth خارجيين

يسهل الربط مع أنظمة مؤسسية خاصة بالمدارس الكبيرة

✅ 4. إشعارات الدخول الآني
إشعار مباشر عند تسجيل دخول من جهاز جديد أو موقع مختلف

دعم إشعارات عبر النظام + البريد الإلكتروني

✅ 5. نظام نسخ احتياطي واسترجاع سريع (Backup & Restore)
تنفيذ نسخ احتياطي تلقائي دوري لقاعدة البيانات

لوحة تحكم لعرض واسترجاع النسخ الاحتياطية السابقة (كامل أو جزئي)

✅ 6. إدارة الجلسات (Session Management UI)
عرض الجلسات النشطة للمستخدم

إمكانية "طرد" جلسة من جهاز معين (logout specific session)

عرض معلومات مثل: IP، الجهاز، وقت الدخول

✅ 7. سجل تغييرات الصلاحيات (Permissions Audit Trail)
تسجيل كل تغيير يتم على الصلاحيات أو RLS:

المستخدم المنفذ

نوع التغيير

الوقت والتاريخ

التفاصيل السابقة والجديدة

✅ 8. مخططات توثيقية لتدفق الأدوار (Role Flow Diagrams)
إنشاء مخططات توضح:

صلاحيات ومسارات كل دور داخل النظام

كيفية انتقال البيانات بناءً على الدور

تستخدم في التوثيق، الاختبارات، والتدريب

✅ 9. قوالب صلاحيات للمدارس الجديدة
عند إنشاء مدرسة جديدة:

يمكن اختيار قالب جاهز لصلاحيات الأدوار

يقلل من الأخطاء اليدوية ويوفر وقت الإعداد

✅ 10. اختبارات وحدات لصلاحيات RLS (RLS Unit Tests)
لكل جدول ومسار:

يتم إنشاء اختبارات تلقائية للتحقق من تطبيق RLS

يتم اختبار تجاوزات الصلاحيات أيضًا

