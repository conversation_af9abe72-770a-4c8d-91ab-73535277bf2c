-- نسخ محتوى الـ migration وتطبيقه
-- إصلاح شامل لسياسات RLS للأدمن

-- تفعيل pgcrypto extension
CREATE EXTENSION IF NOT EXISTS pgcrypto;

-- إنشاء function للتحقق من كون المستخدم أدمن بدون تكرار لا نهائي
CREATE OR REPLACE FUNCTION public.is_admin_user(user_id uuid DEFAULT auth.uid())
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  user_role text;
BEGIN
  -- استعلام مباشر بدون RLS
  SELECT role INTO user_role 
  FROM public.users 
  WHERE id = user_id;
  
  RETURN COALESCE(user_role = 'admin', false);
END;
$$;

-- منح صلاحيات التنفيذ
GRANT EXECUTE ON FUNCTION public.is_admin_user(uuid) TO authenticated;

-- حذف جميع السياسات الموجودة وإعادة إنشائها

-- سياسات جدول المستخدمين
DROP POLICY IF EXISTS "Admin users can access all users" ON public.users;
DROP POLICY IF EXISTS "Users can view own record" ON public.users;
DROP POLICY IF EXISTS "Users can update their own records" ON public.users;
DROP POLICY IF EXISTS "Admin users can update any user" ON public.users;

-- سياسة شاملة للأدمن - يمكنهم الوصول لجميع المستخدمين
CREATE POLICY "admin_full_access_users" ON public.users
FOR ALL TO authenticated
USING (public.is_admin_user() OR id = auth.uid())
WITH CHECK (public.is_admin_user() OR id = auth.uid());

-- سياسات جدول المدارس/المؤسسات
DROP POLICY IF EXISTS "Admin can access all schools" ON public.tenants;
DROP POLICY IF EXISTS "Users can view their tenant" ON public.tenants;

CREATE POLICY "admin_full_access_tenants" ON public.tenants
FOR ALL TO authenticated
USING (
  public.is_admin_user() OR 
  id = (SELECT tenant_id FROM public.users WHERE id = auth.uid())
);

-- سياسات جدول الحافلات
DROP POLICY IF EXISTS "Admin can view all buses" ON public.buses;
DROP POLICY IF EXISTS "Admin can manage all buses" ON public.buses;
DROP POLICY IF EXISTS "Users can view buses in their tenant" ON public.buses;

CREATE POLICY "admin_full_access_buses" ON public.buses
FOR ALL TO authenticated
USING (
  public.is_admin_user() OR 
  tenant_id = (SELECT tenant_id FROM public.users WHERE id = auth.uid())
);

-- سياسات جدول الطرق
DROP POLICY IF EXISTS "Admin can view all routes" ON public.routes;
DROP POLICY IF EXISTS "Admin can manage all routes" ON public.routes;
DROP POLICY IF EXISTS "Users can view own tenant routes" ON public.routes;

CREATE POLICY "admin_full_access_routes" ON public.routes
FOR ALL TO authenticated
USING (
  public.is_admin_user() OR 
  tenant_id = (SELECT tenant_id FROM public.users WHERE id = auth.uid())
);

-- سياسات جدول الطلاب
DROP POLICY IF EXISTS "Admin can view all students" ON public.students;
DROP POLICY IF EXISTS "Admin can manage all students" ON public.students;
DROP POLICY IF EXISTS "Users can view students in their tenant" ON public.students;

CREATE POLICY "admin_full_access_students" ON public.students
FOR ALL TO authenticated
USING (
  public.is_admin_user() OR 
  tenant_id = (SELECT tenant_id FROM public.users WHERE id = auth.uid()) OR
  parent_id = auth.uid()
);

-- سياسات جدول الحضور
DROP POLICY IF EXISTS "Admin can view all attendance" ON public.attendance;
DROP POLICY IF EXISTS "Admin can manage all attendance" ON public.attendance;

CREATE POLICY "admin_full_access_attendance" ON public.attendance
FOR ALL TO authenticated
USING (
  public.is_admin_user() OR 
  tenant_id = (SELECT tenant_id FROM public.users WHERE id = auth.uid())
);

-- إنشاء functions للأدمن للحصول على جميع البيانات
CREATE OR REPLACE FUNCTION public.get_all_users()
RETURNS SETOF public.users
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- التحقق من كون المستخدم أدمن
  IF public.is_admin_user() THEN
    RETURN QUERY SELECT * FROM public.users ORDER BY created_at DESC;
  ELSE
    -- إرجاع المستخدمين في نفس المؤسسة فقط
    RETURN QUERY 
    SELECT u.* FROM public.users u
    WHERE u.tenant_id = (SELECT tenant_id FROM public.users WHERE id = auth.uid())
    ORDER BY u.created_at DESC;
  END IF;
END;
$$;

CREATE OR REPLACE FUNCTION public.get_all_buses()
RETURNS SETOF public.buses
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  IF public.is_admin_user() THEN
    RETURN QUERY SELECT * FROM public.buses ORDER BY created_at DESC;
  ELSE
    RETURN QUERY 
    SELECT b.* FROM public.buses b
    WHERE b.tenant_id = (SELECT tenant_id FROM public.users WHERE id = auth.uid())
    ORDER BY b.created_at DESC;
  END IF;
END;
$$;

CREATE OR REPLACE FUNCTION public.get_all_routes()
RETURNS SETOF public.routes
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  IF public.is_admin_user() THEN
    RETURN QUERY SELECT * FROM public.routes ORDER BY created_at DESC;
  ELSE
    RETURN QUERY 
    SELECT r.* FROM public.routes r
    WHERE r.tenant_id = (SELECT tenant_id FROM public.users WHERE id = auth.uid())
    ORDER BY r.created_at DESC;
  END IF;
END;
$$;

CREATE OR REPLACE FUNCTION public.get_all_students()
RETURNS SETOF public.students
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  IF public.is_admin_user() THEN
    RETURN QUERY SELECT * FROM public.students ORDER BY created_at DESC;
  ELSE
    RETURN QUERY 
    SELECT s.* FROM public.students s
    WHERE s.tenant_id = (SELECT tenant_id FROM public.users WHERE id = auth.uid())
    ORDER BY s.created_at DESC;
  END IF;
END;
$$;

CREATE OR REPLACE FUNCTION public.get_all_tenants()
RETURNS SETOF public.tenants
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  IF public.is_admin_user() THEN
    RETURN QUERY SELECT * FROM public.tenants ORDER BY created_at DESC;
  ELSE
    RETURN QUERY 
    SELECT t.* FROM public.tenants t
    WHERE t.id = (SELECT tenant_id FROM public.users WHERE id = auth.uid())
    ORDER BY t.created_at DESC;
  END IF;
END;
$$;

-- منح صلاحيات التنفيذ لجميع الـ functions
GRANT EXECUTE ON FUNCTION public.get_all_users() TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_all_buses() TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_all_routes() TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_all_students() TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_all_tenants() TO authenticated;

-- إنشاء function لحذف الطلاب بشكل صحيح (حذف المستخدم والطالب معاً)
CREATE OR REPLACE FUNCTION public.delete_student_completely(student_id uuid)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  student_user_id uuid;
  result json;
BEGIN
  -- البحث عن معرف المستخدم المرتبط بالطالب
  SELECT u.id INTO student_user_id
  FROM public.students s
  JOIN public.users u ON u.email = s.name || '@student.local' -- افتراض أن البريد الإلكتروني مبني على الاسم
  WHERE s.id = student_id;

  -- حذف سجل الطالب أولاً
  DELETE FROM public.students WHERE id = student_id;

  -- حذف المستخدم إذا وُجد
  IF student_user_id IS NOT NULL THEN
    DELETE FROM public.users WHERE id = student_user_id;
    DELETE FROM auth.users WHERE id = student_user_id;
  END IF;

  result := json_build_object(
    'success', true,
    'message', 'Student deleted successfully'
  );

  RETURN result;

EXCEPTION
  WHEN OTHERS THEN
    result := json_build_object(
      'success', false,
      'error', SQLERRM,
      'message', 'Failed to delete student'
    );
    RETURN result;
END;
$$;

GRANT EXECUTE ON FUNCTION public.delete_student_completely(uuid) TO authenticated;
