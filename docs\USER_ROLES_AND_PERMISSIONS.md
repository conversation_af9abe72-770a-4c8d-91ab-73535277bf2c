# 👥 أدوار المستخدمين والصلاحيات - نظام إدارة الحافلات المدرسية

## نظرة عامة
هذا المستند يوضح جميع أدوار المستخدمين وصلاحياتهم في نظام إدارة الحافلات المدرسية. النظام يدعم 5 أدوار رئيسية مع صلاحيات محددة لكل دور.

---

## 🔴 1. الأدمن (Admin)
**الدور الأعلى في النظام - صلاحيات كاملة**

### 📊 الإحصائيات:
- ✅ عرض إحصائيات جميع المدارس
- ✅ عرض إحصائيات جميع المستخدمين
- ✅ عرض إحصائيات جميع الحافلات والمسارات
- ✅ عرض إحصائيات الحضور والغياب لجميع الطلاب
- ✅ تصدير جميع التقارير والإحصائيات

### 🏫 إدارة المدارس:
- ✅ عرض جميع المدارس
- ✅ إضافة مدارس جديدة
- ✅ تعديل بيانات أي مدرسة
- ✅ حذف المدارس (مع جميع البيانات المرتبطة)
- ✅ تفعيل/إلغاء تفعيل المدارس
- ✅ العمليات المجمعة للمدارس
- ✅ تعيين مديري المدارس

### 👥 إدارة المستخدمين:
- ✅ عرض جميع المستخدمين في جميع المدارس
- ✅ إضافة مستخدمين جدد لأي مدرسة
- ✅ تعديل بيانات أي مستخدم
- ✅ حذف أي مستخدم
- ✅ تغيير أدوار المستخدمين
- ✅ تفعيل/إلغاء تفعيل المستخدمين
- ✅ إعادة تعيين كلمات المرور

### 🚌 إدارة الحافلات:
- ✅ عرض جميع الحافلات في جميع المدارس
- ✅ إضافة حافلات جديدة
- ✅ تعديل بيانات الحافلات
- ✅ حذف الحافلات
- ✅ تعيين السائقين للحافلات
- ✅ إدارة صيانة الحافلات

### 🛣️ إدارة المسارات:
- ✅ عرض جميع المسارات في جميع المدارس
- ✅ إضافة مسارات جديدة
- ✅ تعديل المسارات
- ✅ حذف المسارات
- ✅ تعيين الحافلات للمسارات

### 🎓 إدارة الطلاب:
- ✅ عرض جميع الطلاب في جميع المدارس
- ✅ إضافة طلاب جدد
- ✅ تعديل بيانات الطلاب
- ✅ حذف الطلاب
- ✅ تسجيل الحضور والغياب
- ✅ ربط الطلاب بأولياء الأمور

---

## 🟡 2. مدير المدرسة (School Manager)
**إدارة مدرسة واحدة فقط**

### 📊 الإحصائيات:
- ✅ عرض إحصائيات مدرسته فقط
- ✅ عرض إحصائيات مستخدمي مدرسته
- ✅ عرض إحصائيات حافلات ومسارات مدرسته
- ✅ عرض إحصائيات حضور وغياب طلاب مدرسته
- ✅ تصدير تقارير مدرسته

### 🏫 إدارة المدرسة:
- ✅ عرض بيانات مدرسته فقط
- ✅ تعديل بيانات مدرسته
- ✅ تفعيل/إلغاء تفعيل مدرسته
- ❌ إضافة مدارس جديدة
- ❌ حذف المدرسة
- ❌ عرض مدارس أخرى

### 👥 إدارة المستخدمين:
- ✅ عرض مستخدمي مدرسته فقط
- ✅ إضافة مستخدمين جدد لمدرسته
- ✅ تعديل بيانات مستخدمي مدرسته
- ✅ حذف مستخدمي مدرسته (عدا الأدمن)
- ✅ تفعيل/إلغاء تفعيل مستخدمي مدرسته
- ❌ تعيين أدمن جديد
- ❌ عرض مستخدمي مدارس أخرى

### 🚌 إدارة الحافلات:
- ✅ عرض حافلات مدرسته فقط
- ✅ إضافة حافلات جديدة لمدرسته
- ✅ تعديل بيانات حافلات مدرسته
- ✅ حذف حافلات مدرسته
- ✅ تعيين السائقين لحافلات مدرسته
- ✅ إدارة صيانة حافلات مدرسته

### 🛣️ إدارة المسارات:
- ✅ عرض مسارات مدرسته فقط
- ✅ إضافة مسارات جديدة لمدرسته
- ✅ تعديل مسارات مدرسته
- ✅ حذف مسارات مدرسته
- ✅ تعيين حافلات مدرسته للمسارات

### 🎓 إدارة الطلاب:
- ✅ عرض طلاب مدرسته فقط
- ✅ إضافة طلاب جدد لمدرسته
- ✅ تعديل بيانات طلاب مدرسته
- ✅ حذف طلاب مدرسته
- ✅ تسجيل حضور وغياب طلاب مدرسته

---

## 🟢 3. المشرف (Supervisor)
**مراقبة ومتابعة - قراءة فقط**

### 📊 الإحصائيات:
- ✅ عرض إحصائيات مدرسته فقط (قراءة فقط)
- ✅ عرض إحصائيات مستخدمي مدرسته
- ✅ عرض إحصائيات حافلات ومسارات مدرسته
- ✅ عرض إحصائيات حضور وغياب طلاب مدرسته
- ✅ تصدير تقارير مدرسته

### 🏫 إدارة المدرسة:
- ✅ عرض بيانات مدرسته فقط
- ❌ تعديل أي بيانات
- ❌ إضافة أو حذف

### 👥 إدارة المستخدمين:
- ✅ عرض مستخدمي مدرسته فقط
- ❌ إضافة أو تعديل أو حذف مستخدمين

### 🚌 إدارة الحافلات:
- ✅ عرض حافلات مدرسته فقط
- ❌ إضافة أو تعديل أو حذف حافلات

### 🛣️ إدارة المسارات:
- ✅ عرض مسارات مدرسته فقط
- ❌ إضافة أو تعديل أو حذف مسارات

### 🎓 إدارة الطلاب:
- ✅ عرض طلاب مدرسته فقط
- ❌ إضافة أو تعديل أو حذف طلاب
- ❌ تسجيل حضور وغياب

---

## 🔵 4. السائق (Driver)
**إدارة حافلته ومساره**

### 📊 الإحصائيات:
- ✅ عرض إحصائيات حافلته فقط
- ✅ عرض إحصائيات مساره
- ✅ عرض إحصائيات طلاب مساره
- ✅ عرض حضور وغياب طلاب رحلته

### 🚌 إدارة الحافلة:
- ✅ عرض بيانات حافلته فقط
- ✅ تحديث حالة الحافلة
- ✅ تسجيل مشاكل الصيانة
- ❌ تعديل بيانات الحافلة الأساسية

### 🛣️ إدارة المسار:
- ✅ عرض مساره فقط
- ✅ تحديث موقع الحافلة
- ❌ تعديل المسار

### 🎓 إدارة الطلاب:
- ✅ عرض طلاب مساره فقط
- ✅ تسجيل صعود ونزول الطلاب
- ✅ تسجيل حضور الطلاب في الرحلة
- ❌ تعديل بيانات الطلاب

### 📱 التطبيق المحمول:
- ✅ تطبيق سائق مخصص
- ✅ تتبع GPS للحافلة
- ✅ إشعارات الرحلات
- ✅ تسجيل الحضور بالباركود

---

## 🟣 5. ولي الأمر (Parent)
**متابعة أطفاله فقط**

### 📊 الإحصائيات:
- ✅ عرض إحصائيات أطفاله فقط
- ✅ عرض حضور وغياب أطفاله
- ✅ عرض تقارير أطفاله الشهرية

### 🎓 متابعة الأطفال:
- ✅ عرض بيانات أطفاله فقط
- ✅ عرض حضور وغياب أطفاله
- ✅ عرض الحافلة المخصصة لأطفاله
- ✅ عرض المسار الذي يستخدمه أطفاله
- ❌ تعديل أي بيانات

### 🚌 متابعة النقل:
- ✅ تتبع موقع حافلة أطفاله
- ✅ عرض مواعيد الرحلات
- ✅ إشعارات وصول الحافلة
- ✅ إشعارات صعود ونزول الأطفال

### 📱 التطبيق المحمول:
- ✅ تطبيق ولي أمر مخصص
- ✅ تتبع مباشر للأطفال
- ✅ إشعارات فورية
- ✅ تقارير يومية وأسبوعية

---

## 🔒 ملخص الصلاحيات - جدول مقارن

| الوظيفة | الأدمن | مدير المدرسة | المشرف | السائق | ولي الأمر |
|---------|--------|-------------|--------|---------|-----------|
| **عرض جميع المدارس** | ✅ | ❌ | ❌ | ❌ | ❌ |
| **إدارة مدرسته** | ✅ | ✅ | 👁️ | ❌ | ❌ |
| **إدارة جميع المستخدمين** | ✅ | ❌ | ❌ | ❌ | ❌ |
| **إدارة مستخدمي مدرسته** | ✅ | ✅ | 👁️ | ❌ | ❌ |
| **إدارة جميع الحافلات** | ✅ | ❌ | ❌ | ❌ | ❌ |
| **إدارة حافلات مدرسته** | ✅ | ✅ | 👁️ | ❌ | ❌ |
| **إدارة حافلته** | ✅ | ✅ | ❌ | ✅ | ❌ |
| **إدارة جميع المسارات** | ✅ | ❌ | ❌ | ❌ | ❌ |
| **إدارة مسارات مدرسته** | ✅ | ✅ | 👁️ | ❌ | ❌ |
| **إدارة مساره** | ✅ | ✅ | ❌ | 👁️ | ❌ |
| **إدارة جميع الطلاب** | ✅ | ❌ | ❌ | ❌ | ❌ |
| **إدارة طلاب مدرسته** | ✅ | ✅ | 👁️ | ❌ | ❌ |
| **متابعة طلاب مساره** | ✅ | ✅ | ✅ | ✅ | ❌ |
| **متابعة أطفاله** | ✅ | ✅ | ✅ | ✅ | ✅ |
| **تسجيل الحضور والغياب** | ✅ | ✅ | ❌ | ✅ | ❌ |
| **تصدير التقارير** | ✅ | ✅ | ✅ | ❌ | ❌ |
| **العمليات المجمعة** | ✅ | ✅ | ❌ | ❌ | ❌ |
| **تعيين الأدوار** | ✅ | ❌ | ❌ | ❌ | ❌ |
| **حذف البيانات** | ✅ | ✅* | ❌ | ❌ | ❌ |

**الرموز:**
- ✅ = صلاحية كاملة (قراءة + كتابة + حذف)
- 👁️ = قراءة فقط
- ❌ = لا يوجد وصول
- * = مقيد بمدرسته فقط

---

## 🛡️ تطبيق الصلاحيات في النظام

### Row Level Security (RLS)
النظام يستخدم سياسات RLS في قاعدة البيانات لضمان:
- عزل البيانات بين المدارس المختلفة
- منع الوصول غير المصرح به
- تطبيق الصلاحيات على مستوى قاعدة البيانات

### Frontend Permissions
- استخدام `usePermissions` hook للتحقق من الصلاحيات
- إخفاء/إظهار العناصر حسب الدور
- منع الوصول للصفحات غير المصرح بها

### API Security
- التحقق من الصلاحيات في كل API call
- تشفير البيانات الحساسة
- تسجيل جميع العمليات في audit logs

---

## 📝 ملاحظات مهمة

1. **الأدمن الرئيسي**: لا يمكن حذفه أو تعديل دوره
2. **مدير المدرسة**: يمكن أن يكون هناك مدير واحد فقط لكل مدرسة
3. **السائق**: مرتبط بحافلة واحدة فقط
4. **ولي الأمر**: يمكن أن يكون له عدة أطفال
5. **المشرف**: دور مراقبة فقط بدون صلاحيات تعديل

---

## 🔄 تحديث الأدوار

لتحديث دور مستخدم:
1. يجب أن يكون المستخدم الحالي أدمن
2. لا يمكن تحديث دور الأدمن الرئيسي
3. عند تغيير الدور، يتم تحديث الصلاحيات فوراً
4. يتم تسجيل التغيير في audit logs

---

**تاريخ آخر تحديث:** ديسمبر 2024
**الإصدار:** 1.0
**المطور:** نظام إدارة الحافلات المدرسية
