    -- تعطيل RLS فقط لحل مشكلة عدم ظهور البيانات
    -- Disable R<PERSON> only to fix data visibility issue

    -- تعطيل RLS على جميع الجداول
    -- Disable RLS on all tables
    ALTER TABLE users DISABLE ROW LEVEL SECURITY;
    ALTER TABLE students DISABLE ROW LEVEL SECURITY;
    ALTER TABLE buses DISABLE ROW LEVEL SECURITY;
    ALTER TABLE routes DISABLE ROW LEVEL SECURITY;
    ALTER TABLE tenants DISABLE ROW LEVEL SECURITY;
    ALTER TABLE attendance DISABLE ROW LEVEL SECURITY;

    -- حذف جميع السياسات المتضاربة
    -- Drop all conflicting policies
    DROP POLICY IF EXISTS "admin_full_access" ON users;
    DROP POLICY IF EXISTS "school_manager_tenant_access" ON users;
    DROP POLICY IF EXISTS "own_profile_access" ON users;
    DROP POLICY IF EXISTS "tenant_users_view" ON users;
    DROP POLICY IF EXISTS "users_own_profile" ON users;
    DROP POLICY IF EXISTS "admin_access_all" ON users;
    DROP POLICY IF EXISTS "school_manager_tenant" ON users;

    DROP POLICY IF EXISTS "admin_full_access" ON students;
    DROP POLICY IF EXISTS "school_staff_tenant_access" ON students;
    DROP POLICY IF EXISTS "parent_children_access" ON students;
    DROP POLICY IF EXISTS "student_own_profile" ON students;
    DROP POLICY IF EXISTS "driver_tenant_view" ON students;

    DROP POLICY IF EXISTS "admin_full_access" ON buses;
    DROP POLICY IF EXISTS "school_staff_tenant_access" ON buses;
    DROP POLICY IF EXISTS "driver_assigned_bus" ON buses;
    DROP POLICY IF EXISTS "tenant_buses_view" ON buses;

    DROP POLICY IF EXISTS "admin_full_access" ON routes;
    DROP POLICY IF EXISTS "school_staff_tenant_access" ON routes;
    DROP POLICY IF EXISTS "tenant_routes_view" ON routes;

    DROP POLICY IF EXISTS "admin_full_access" ON tenants;
    DROP POLICY IF EXISTS "school_manager_own_tenant" ON tenants;
    DROP POLICY IF EXISTS "users_own_tenant_view" ON tenants;

    DROP POLICY IF EXISTS "admin_full_access" ON attendance;
    DROP POLICY IF EXISTS "tenant_staff_access" ON attendance;
    DROP POLICY IF EXISTS "parent_children_attendance" ON attendance;

    -- رسالة تأكيد
    SELECT 'RLS disabled successfully on all tables' as status;
