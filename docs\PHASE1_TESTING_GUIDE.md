# دليل اختبار المرحلة الأولى: نظام الصلاحيات المركزي
# Phase 1 Testing Guide: Centralized Permission System

**الإصدار:** 1.0  
**التاريخ:** 30 يناير 2025  
**الحالة:** جاهز للاختبار ✅  

---

## 📋 نظرة عامة

هذا الدليل يوضح كيفية اختبار نظام الصلاحيات المركزي الجديد للتأكد من عمله بشكل صحيح وآمن.

---

## 🔧 الإعداد المطلوب

### 1. متطلبات البيئة
```bash
# تأكد من وجود المتغيرات البيئية
VITE_SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_service_key
```

### 2. تطبيق التهجيرات
```bash
# تشغيل سكريبت تطبيق المرحلة الأولى
npm run apply-phase1-migrations

# أو يدوياً
npx tsx scripts/apply-phase1-migrations.ts
```

### 3. التحقق من التطبيق
```sql
-- فحص سلامة النظام
SELECT * FROM check_permission_system_integrity();

-- عرض حالة النظام
SELECT * FROM permission_system_status;
```

---

## 🧪 اختبارات قاعدة البيانات

### 1. اختبار الدوال الأساسية

#### اختبار دالة التحقق من الأدمن:
```sql
-- اختبار مع مستخدم أدمن
SELECT is_system_admin('admin-user-id');
-- النتيجة المتوقعة: true

-- اختبار مع مستخدم عادي
SELECT is_system_admin('regular-user-id');
-- النتيجة المتوقعة: false
```

#### اختبار دالة الحصول على الدور:
```sql
-- اختبار الحصول على دور المستخدم
SELECT get_user_role_secure('user-id');
-- النتيجة المتوقعة: 'admin', 'school_manager', etc.

-- اختبار مع معرف غير موجود
SELECT get_user_role_secure('non-existent-id');
-- النتيجة المتوقعة: 'student' (القيمة الافتراضية)
```

#### اختبار دالة الحصول على المستأجر:
```sql
-- اختبار الحصول على معرف المستأجر
SELECT get_user_tenant_secure('user-id');
-- النتيجة المتوقعة: uuid أو null

-- اختبار مع مستخدم أدمن (بدون مستأجر)
SELECT get_user_tenant_secure('admin-user-id');
-- النتيجة المتوقعة: null
```

### 2. اختبار دالة فحص الصلاحيات

#### اختبار صلاحيات الأدمن:
```sql
-- الأدمن يجب أن يكون له صلاحية على كل شيء
SELECT check_permission(
  'admin-user-id',
  'user',
  'create',
  null,
  null,
  '{}'::jsonb
);
-- النتيجة المتوقعة: true
```

#### اختبار صلاحيات مدير المدرسة:
```sql
-- مدير المدرسة يجب أن يكون له صلاحية على مستأجره
SELECT check_permission(
  'school-manager-id',
  'student',
  'create',
  'his-tenant-id',
  null,
  '{"target_tenant_id": "his-tenant-id"}'::jsonb
);
-- النتيجة المتوقعة: true

-- مدير المدرسة لا يجب أن يكون له صلاحية على مستأجر آخر
SELECT check_permission(
  'school-manager-id',
  'student',
  'create',
  'other-tenant-id',
  null,
  '{"target_tenant_id": "other-tenant-id"}'::jsonb
);
-- النتيجة المتوقعة: false
```

#### اختبار صلاحيات ولي الأمر:
```sql
-- ولي الأمر يجب أن يكون له صلاحية على أطفاله فقط
SELECT check_permission(
  'parent-id',
  'student',
  'read',
  'tenant-id',
  'parent-id',
  '{"resource_id": "his-child-id"}'::jsonb
);
-- النتيجة المتوقعة: true

-- ولي الأمر لا يجب أن يكون له صلاحية على طفل آخر
SELECT check_permission(
  'parent-id',
  'student',
  'read',
  'tenant-id',
  'other-parent-id',
  '{"resource_id": "other-child-id"}'::jsonb
);
-- النتيجة المتوقعة: false
```

### 3. اختبار السياسات (RLS)

#### اختبار سياسات جدول المستخدمين:
```sql
-- تسجيل الدخول كأدمن
SET LOCAL role = 'authenticated';
SET LOCAL "request.jwt.claims" = '{"sub": "admin-user-id", "role": "admin"}';

-- يجب أن يرى جميع المستخدمين
SELECT COUNT(*) FROM users;

-- تسجيل الدخول كمدير مدرسة
SET LOCAL "request.jwt.claims" = '{"sub": "school-manager-id", "role": "school_manager", "tenant_id": "tenant-id"}';

-- يجب أن يرى مستخدمي مدرسته فقط
SELECT COUNT(*) FROM users WHERE tenant_id = 'tenant-id';
```

#### اختبار سياسات جدول الطلاب:
```sql
-- تسجيل الدخول كولي أمر
SET LOCAL "request.jwt.claims" = '{"sub": "parent-id", "role": "parent", "tenant_id": "tenant-id"}';

-- يجب أن يرى أطفاله فقط
SELECT COUNT(*) FROM students WHERE parent_id = 'parent-id';

-- محاولة الوصول لطالب آخر (يجب أن تفشل)
SELECT * FROM students WHERE parent_id != 'parent-id';
-- النتيجة المتوقعة: 0 صفوف
```

---

## 💻 اختبارات الكود البرمجي

### 1. اختبار خدمة الصلاحيات المركزية

#### إعداد الاختبار:
```typescript
import { CentralizedPermissionService, ResourceType, Action } from '../services/CentralizedPermissionService';

const permissionService = CentralizedPermissionService.getInstance();

// بيانات اختبار
const adminUser = {
  id: 'admin-id',
  role: 'admin',
  tenant_id: null
};

const schoolManagerUser = {
  id: 'manager-id', 
  role: 'school_manager',
  tenant_id: 'school-1'
};

const parentUser = {
  id: 'parent-id',
  role: 'parent', 
  tenant_id: 'school-1'
};
```

#### اختبار صلاحيات الأدمن:
```typescript
describe('Admin Permissions', () => {
  test('Admin can create users globally', async () => {
    const result = await permissionService.checkPermission(
      adminUser,
      ResourceType.USER,
      Action.CREATE
    );
    
    expect(result.allowed).toBe(true);
  });
  
  test('Admin can access any tenant', async () => {
    const canAccess = await permissionService.canAccessTenant(adminUser, 'any-tenant-id');
    expect(canAccess).toBe(true);
  });
});
```

#### اختبار صلاحيات مدير المدرسة:
```typescript
describe('School Manager Permissions', () => {
  test('School manager can manage users in their tenant', async () => {
    const result = await permissionService.checkPermission(
      schoolManagerUser,
      ResourceType.USER,
      Action.UPDATE,
      { resourceTenantId: 'school-1' }
    );
    
    expect(result.allowed).toBe(true);
  });
  
  test('School manager cannot manage users in other tenants', async () => {
    const result = await permissionService.checkPermission(
      schoolManagerUser,
      ResourceType.USER,
      Action.UPDATE,
      { resourceTenantId: 'school-2' }
    );
    
    expect(result.allowed).toBe(false);
  });
});
```

#### اختبار صلاحيات ولي الأمر:
```typescript
describe('Parent Permissions', () => {
  test('Parent can view their children', async () => {
    const result = await permissionService.checkPermission(
      parentUser,
      ResourceType.STUDENT,
      Action.READ,
      { 
        resourceOwnerId: 'parent-id',
        additionalData: { resource_id: 'child-id' }
      }
    );
    
    expect(result.allowed).toBe(true);
  });
  
  test('Parent cannot view other children', async () => {
    const result = await permissionService.checkPermission(
      parentUser,
      ResourceType.STUDENT,
      Action.READ,
      { 
        resourceOwnerId: 'other-parent-id',
        additionalData: { resource_id: 'other-child-id' }
      }
    );
    
    expect(result.allowed).toBe(false);
  });
});
```

### 2. اختبار React Hooks

#### اختبار usePermission Hook:
```typescript
import { renderHook, waitFor } from '@testing-library/react';
import { usePermission } from '../hooks/useCentralizedPermissions';

describe('usePermission Hook', () => {
  test('Returns correct permission result', async () => {
    const { result } = renderHook(() => 
      usePermission(ResourceType.USER, Action.CREATE)
    );
    
    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });
    
    expect(result.current.allowed).toBeDefined();
    expect(typeof result.current.allowed).toBe('boolean');
  });
});
```

#### اختبار useResourcePermissions Hook:
```typescript
describe('useResourcePermissions Hook', () => {
  test('Returns permission summary', async () => {
    const { result } = renderHook(() => 
      useResourcePermissions(ResourceType.STUDENT)
    );
    
    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });
    
    expect(result.current).toHaveProperty('canCreate');
    expect(result.current).toHaveProperty('canRead');
    expect(result.current).toHaveProperty('canUpdate');
    expect(result.current).toHaveProperty('canDelete');
  });
});
```

---

## 🔒 اختبارات الأمان

### 1. اختبار عزل البيانات

#### اختبار منع تسرب البيانات بين المستأجرين:
```sql
-- إنشاء بيانات اختبار
INSERT INTO tenants (id, name) VALUES 
  ('tenant-1', 'School A'),
  ('tenant-2', 'School B');

INSERT INTO users (id, role, tenant_id, name, email) VALUES
  ('user-1', 'school_manager', 'tenant-1', 'Manager A', '<EMAIL>'),
  ('user-2', 'school_manager', 'tenant-2', 'Manager B', '<EMAIL>');

-- تسجيل الدخول كمدير المدرسة الأولى
SET LOCAL "request.jwt.claims" = '{"sub": "user-1", "role": "school_manager", "tenant_id": "tenant-1"}';

-- يجب ألا يرى بيانات المدرسة الثانية
SELECT COUNT(*) FROM users WHERE tenant_id = 'tenant-2';
-- النتيجة المتوقعة: 0
```

### 2. اختبار مقاومة SQL Injection

#### اختبار حقن SQL في دالة فحص الصلاحيات:
```sql
-- محاولة حقن SQL
SELECT check_permission(
  'user-id',
  'user; DROP TABLE users; --',
  'read',
  null,
  null,
  '{}'::jsonb
);
-- يجب أن تفشل بأمان دون تنفيذ الأمر الضار
```

### 3. اختبار التسجيل الأمني

#### اختبار تسجيل محاولات الوصول المرفوضة:
```sql
-- محاولة وصول غير مصرح بها
SELECT check_permission(
  'unauthorized-user-id',
  'admin_panel',
  'access',
  null,
  null,
  '{}'::jsonb
);

-- التحقق من تسجيل الحدث
SELECT * FROM security_events 
WHERE event_type = 'PERMISSION_DENIED' 
  AND user_id = 'unauthorized-user-id'
ORDER BY created_at DESC 
LIMIT 1;
```

---

## 📊 اختبارات الأداء

### 1. اختبار سرعة فحص الصلاحيات

```sql
-- قياس وقت تنفيذ فحص الصلاحيات
EXPLAIN ANALYZE 
SELECT check_permission(
  'user-id',
  'student',
  'read',
  'tenant-id',
  null,
  '{}'::jsonb
);
-- يجب أن يكون الوقت أقل من 10ms
```

### 2. اختبار الأداء تحت الضغط

```typescript
describe('Performance Tests', () => {
  test('Permission check performance under load', async () => {
    const startTime = Date.now();
    
    // تنفيذ 1000 فحص صلاحية متزامن
    const promises = Array.from({ length: 1000 }, () =>
      permissionService.checkPermission(
        testUser,
        ResourceType.STUDENT,
        Action.READ
      )
    );
    
    await Promise.all(promises);
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    // يجب أن يكتمل في أقل من 5 ثوان
    expect(duration).toBeLessThan(5000);
  });
});
```

---

## ✅ قائمة التحقق النهائية

### قبل النشر:
- [ ] جميع اختبارات قاعدة البيانات تمر بنجاح
- [ ] جميع اختبارات الكود البرمجي تمر بنجاح  
- [ ] اختبارات الأمان تؤكد عدم وجود ثغرات
- [ ] اختبارات الأداء تحقق المعايير المطلوبة
- [ ] فحص سلامة النظام يعطي حالة "HEALTHY"
- [ ] جميع الوثائق محدثة

### بعد النشر:
- [ ] مراقبة الأداء في البيئة الإنتاجية
- [ ] مراجعة سجلات الأمان يومياً
- [ ] تشغيل اختبارات التكامل أسبوعياً
- [ ] مراجعة تقارير النظام شهرياً

---

## 🆘 استكشاف الأخطاء

### مشاكل شائعة وحلولها:

#### 1. فشل في تطبيق التهجيرات:
```bash
# إعادة تعيين النظام
SELECT emergency_permission_reset();

# إعادة تطبيق التهجيرات
npm run apply-phase1-migrations
```

#### 2. مشاكل في الصلاحيات:
```sql
-- فحص حالة النظام
SELECT * FROM check_permission_system_integrity();

-- مراجعة مصفوفة الصلاحيات
SELECT * FROM permission_matrix WHERE is_active = true;
```

#### 3. مشاكل في الأداء:
```sql
-- فحص الفهارس
SELECT * FROM pg_indexes WHERE tablename IN ('users', 'permission_matrix');

-- إعادة بناء الإحصائيات
ANALYZE users, permission_matrix, security_events;
```

---

## 📞 الدعم

للحصول على المساعدة:
- **الوثائق:** راجع `PHASE1_SECURITY_RESTRUCTURING_REPORT.md`
- **الكود:** راجع `src/services/CentralizedPermissionService.ts`
- **قاعدة البيانات:** استخدم `check_permission_system_integrity()`
