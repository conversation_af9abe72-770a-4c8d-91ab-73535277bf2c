-- ===================================================================
-- المرحلة الأولى: إنهاء وتفعيل نظام الصلاحيات المركزي
-- Phase 1: Finalization and Activation of Centralized Permission System
-- Generated: 2025-01-30
-- ===================================================================

-- ===== 1. إنشاء جدول تسجيل التغييرات =====
-- Create change log table

CREATE TABLE IF NOT EXISTS public.permission_change_log (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES public.users(id),
  tenant_id uuid REFERENCES public.tenants(id),
  change_type text NOT NULL, -- 'POLICY_CREATED', 'POLICY_UPDATED', 'POLICY_DELETED', 'PERMISSION_GRANTED', 'PERMISSION_REVOKED'
  resource_type text NOT NULL,
  action text NOT NULL,
  old_value jsonb,
  new_value jsonb,
  reason text,
  ip_address text,
  user_agent text,
  created_at timestamptz DEFAULT now(),
  
  CHECK (change_type IN ('POLICY_CREATED', 'POLICY_UPDATED', 'POLICY_DELETED', 'PERMISSION_GRANTED', 'PERMISSION_REVOKED'))
);

-- إنشاء فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_permission_change_log_user_time 
ON public.permission_change_log(user_id, created_at DESC);

CREATE INDEX IF NOT EXISTS idx_permission_change_log_tenant_time 
ON public.permission_change_log(tenant_id, created_at DESC);

CREATE INDEX IF NOT EXISTS idx_permission_change_log_type_time 
ON public.permission_change_log(change_type, created_at DESC);

-- تفعيل RLS على جدول تسجيل التغييرات
ALTER TABLE public.permission_change_log ENABLE ROW LEVEL SECURITY;

-- سياسة للوصول لتسجيل التغييرات (الأدمن فقط)
CREATE POLICY "permission_change_log_admin_access" ON public.permission_change_log
FOR ALL TO authenticated
USING (public.is_system_admin())
WITH CHECK (public.is_system_admin());

-- ===== 2. إنشاء دالة لتسجيل التغييرات =====
-- Create function for logging changes

CREATE OR REPLACE FUNCTION public.log_permission_change(
  change_type text,
  resource_type text,
  action text,
  old_value jsonb DEFAULT null,
  new_value jsonb DEFAULT null,
  reason text DEFAULT null,
  user_id uuid DEFAULT auth.uid(),
  tenant_id uuid DEFAULT null
)
RETURNS uuid
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  log_id uuid;
BEGIN
  INSERT INTO public.permission_change_log (
    user_id,
    tenant_id,
    change_type,
    resource_type,
    action,
    old_value,
    new_value,
    reason,
    ip_address,
    user_agent,
    created_at
  ) VALUES (
    COALESCE(user_id, auth.uid()),
    COALESCE(tenant_id, public.get_user_tenant_secure()),
    change_type,
    resource_type,
    action,
    old_value,
    new_value,
    reason,
    COALESCE(current_setting('request.headers', true)::json->>'x-forwarded-for', 'unknown'),
    COALESCE(current_setting('request.headers', true)::json->>'user-agent', 'unknown'),
    now()
  ) RETURNING id INTO log_id;
  
  RETURN log_id;
EXCEPTION
  WHEN OTHERS THEN
    -- في حالة فشل التسجيل، لا نريد أن نوقف العملية
    RETURN null;
END;
$$;

-- منح الصلاحية للدالة
GRANT EXECUTE ON FUNCTION public.log_permission_change(text, text, text, jsonb, jsonb, text, uuid, uuid) TO authenticated;

-- ===== 3. إنشاء دالة للتحقق من سلامة النظام =====
-- Create system integrity check function

CREATE OR REPLACE FUNCTION public.check_permission_system_integrity()
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  result jsonb := '{}';
  policy_count integer;
  function_count integer;
  matrix_count integer;
  orphaned_policies integer;
  missing_permissions integer;
BEGIN
  -- فحص عدد السياسات
  SELECT COUNT(*) INTO policy_count
  FROM pg_policies 
  WHERE schemaname = 'public';
  
  -- فحص عدد الدوال الأمنية
  SELECT COUNT(*) INTO function_count
  FROM information_schema.routines 
  WHERE routine_schema = 'public' 
    AND routine_name IN ('is_system_admin', 'get_user_role_secure', 'get_user_tenant_secure', 'check_permission');
  
  -- فحص عدد إدخالات مصفوفة الصلاحيات
  SELECT COUNT(*) INTO matrix_count
  FROM public.permission_matrix
  WHERE is_active = true;
  
  -- فحص السياسات المعزولة (بدون دوال مساعدة)
  SELECT COUNT(*) INTO orphaned_policies
  FROM pg_policies p
  WHERE schemaname = 'public'
    AND NOT EXISTS (
      SELECT 1 FROM information_schema.routines r
      WHERE r.routine_schema = 'public'
        AND p.qual LIKE '%' || r.routine_name || '%'
    );
  
  -- فحص الصلاحيات المفقودة للأدوار
  SELECT COUNT(*) INTO missing_permissions
  FROM (
    SELECT DISTINCT role FROM public.users
  ) u
  WHERE NOT EXISTS (
    SELECT 1 FROM public.permission_matrix pm
    WHERE pm.role = u.role AND pm.is_active = true
  );
  
  -- بناء النتيجة
  result := jsonb_build_object(
    'system_status', CASE 
      WHEN function_count = 4 AND matrix_count > 0 AND missing_permissions = 0 THEN 'HEALTHY'
      WHEN function_count >= 3 AND matrix_count > 0 THEN 'WARNING'
      ELSE 'CRITICAL'
    END,
    'policy_count', policy_count,
    'function_count', function_count,
    'matrix_entries', matrix_count,
    'orphaned_policies', orphaned_policies,
    'missing_permissions', missing_permissions,
    'last_check', now(),
    'recommendations', CASE
      WHEN function_count < 4 THEN jsonb_build_array('Missing security functions')
      WHEN matrix_count = 0 THEN jsonb_build_array('Empty permission matrix')
      WHEN missing_permissions > 0 THEN jsonb_build_array('Users with missing permissions')
      ELSE jsonb_build_array('System is healthy')
    END
  );
  
  RETURN result;
EXCEPTION
  WHEN OTHERS THEN
    RETURN jsonb_build_object(
      'system_status', 'ERROR',
      'error', SQLERRM,
      'last_check', now()
    );
END;
$$;

-- منح الصلاحية للدالة
GRANT EXECUTE ON FUNCTION public.check_permission_system_integrity() TO authenticated;

-- ===== 4. إنشاء دالة لإعادة تعيين النظام في حالة الطوارئ =====
-- Create emergency system reset function

CREATE OR REPLACE FUNCTION public.emergency_permission_reset()
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  result jsonb;
  current_user_role text;
BEGIN
  -- التحقق من أن المستخدم أدمن
  SELECT role INTO current_user_role FROM public.users WHERE id = auth.uid();
  
  IF current_user_role != 'admin' THEN
    RETURN jsonb_build_object(
      'success', false,
      'error', 'PERMISSION_DENIED',
      'message', 'Only system administrators can perform emergency reset'
    );
  END IF;
  
  -- تسجيل الحدث
  PERFORM public.log_security_event(
    'EMERGENCY_RESET_INITIATED',
    'WARNING',
    'Emergency permission system reset initiated',
    auth.uid(),
    null,
    jsonb_build_object('timestamp', now())
  );
  
  -- تعطيل RLS مؤقتاً
  ALTER TABLE public.users DISABLE ROW LEVEL SECURITY;
  ALTER TABLE public.tenants DISABLE ROW LEVEL SECURITY;
  ALTER TABLE public.buses DISABLE ROW LEVEL SECURITY;
  ALTER TABLE public.routes DISABLE ROW LEVEL SECURITY;
  ALTER TABLE public.students DISABLE ROW LEVEL SECURITY;
  ALTER TABLE public.attendance DISABLE ROW LEVEL SECURITY;
  
  -- إعادة تفعيل RLS
  ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
  ALTER TABLE public.tenants ENABLE ROW LEVEL SECURITY;
  ALTER TABLE public.buses ENABLE ROW LEVEL SECURITY;
  ALTER TABLE public.routes ENABLE ROW LEVEL SECURITY;
  ALTER TABLE public.students ENABLE ROW LEVEL SECURITY;
  ALTER TABLE public.attendance ENABLE ROW LEVEL SECURITY;
  
  -- تسجيل إتمام العملية
  PERFORM public.log_security_event(
    'EMERGENCY_RESET_COMPLETED',
    'INFO',
    'Emergency permission system reset completed successfully',
    auth.uid(),
    null,
    jsonb_build_object('timestamp', now())
  );
  
  RETURN jsonb_build_object(
    'success', true,
    'message', 'Emergency reset completed successfully',
    'timestamp', now()
  );
  
EXCEPTION
  WHEN OTHERS THEN
    RETURN jsonb_build_object(
      'success', false,
      'error', 'RESET_FAILED',
      'message', 'Emergency reset failed: ' || SQLERRM,
      'timestamp', now()
    );
END;
$$;

-- منح الصلاحية للدالة (للأدمن فقط)
GRANT EXECUTE ON FUNCTION public.emergency_permission_reset() TO authenticated;

-- ===== 5. إنشاء view لمراقبة النظام =====
-- Create system monitoring view

CREATE OR REPLACE VIEW public.permission_system_status AS
SELECT 
  'permission_matrix' as component,
  COUNT(*) as total_entries,
  COUNT(*) FILTER (WHERE is_active = true) as active_entries,
  COUNT(DISTINCT role) as unique_roles,
  COUNT(DISTINCT resource_type) as unique_resources,
  now() as last_updated
FROM public.permission_matrix
UNION ALL
SELECT 
  'rls_policies' as component,
  COUNT(*) as total_entries,
  COUNT(*) as active_entries,
  COUNT(DISTINCT tablename) as unique_roles,
  0 as unique_resources,
  now() as last_updated
FROM pg_policies 
WHERE schemaname = 'public'
UNION ALL
SELECT 
  'security_functions' as component,
  COUNT(*) as total_entries,
  COUNT(*) as active_entries,
  0 as unique_roles,
  0 as unique_resources,
  now() as last_updated
FROM information_schema.routines 
WHERE routine_schema = 'public' 
  AND routine_name IN ('is_system_admin', 'get_user_role_secure', 'get_user_tenant_secure', 'check_permission');

-- منح الصلاحية للعرض
GRANT SELECT ON public.permission_system_status TO authenticated;

-- ===== 6. تسجيل إتمام المرحلة الأولى =====
-- Log Phase 1 completion

SELECT public.log_security_event(
  'PHASE1_COMPLETED',
  'INFO',
  'Phase 1 centralized permission system implementation completed successfully',
  auth.uid(),
  null,
  jsonb_build_object(
    'phase', 1,
    'completion_date', now(),
    'components_created', jsonb_build_array(
      'centralized_permission_service',
      'unified_rls_policies', 
      'permission_matrix',
      'security_functions',
      'monitoring_tools'
    ),
    'next_phase', 'Phase 2: New Features Development'
  )
);

-- إضافة تعليق على المخطط
COMMENT ON SCHEMA public IS 'Phase 1 Centralized Permission System - COMPLETED';

-- إنشاء فهرس نهائي للأداء
CREATE INDEX IF NOT EXISTS idx_users_role_tenant_active 
ON public.users(role, tenant_id, is_active) WHERE is_active = true;

CREATE INDEX IF NOT EXISTS idx_permission_matrix_lookup 
ON public.permission_matrix(role, resource_type, action, is_active) WHERE is_active = true;
