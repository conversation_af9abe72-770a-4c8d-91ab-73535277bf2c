-- إصلاحا<PERSON> قاعدة البيانات السريعة
-- Quick Database Fixes

-- ===== 1. تعطيل RLS مؤقت<|im_start|> =====
-- Temporarily disable RLS

ALTER TABLE users DISABLE ROW LEVEL SECURITY;
ALTER TABLE students DISABLE ROW LEVEL SECURITY;
ALTER TABLE buses DISABLE ROW LEVEL SECURITY;
ALTER TABLE routes DISABLE ROW LEVEL SECURITY;
ALTER TABLE tenants DISABLE ROW LEVEL SECURITY;
ALTER TABLE attendance DISABLE ROW LEVEL SECURITY;

-- ===== 2. حذف السياسات المتضاربة =====
-- Drop conflicting policies

DROP POLICY IF EXISTS "admin_full_access" ON users;
DROP POLICY IF EXISTS "school_manager_tenant_access" ON users;
DROP POLICY IF EXISTS "own_profile_access" ON users;
DROP POLICY IF EXISTS "tenant_users_view" ON users;
DROP POLICY IF EXISTS "users_own_profile" ON users;
DROP POLICY IF EXISTS "admin_access_all" ON users;
DROP POLICY IF EXISTS "school_manager_tenant" ON users;

DROP POLICY IF EXISTS "admin_full_access" ON students;
DROP POLICY IF EXISTS "school_staff_tenant_access" ON students;
DROP POLICY IF EXISTS "parent_children_access" ON students;
DROP POLICY IF EXISTS "student_own_profile" ON students;
DROP POLICY IF EXISTS "driver_tenant_view" ON students;

-- ===== 3. إنشاء دوال مساعدة آمنة =====
-- Create safe helper functions

CREATE OR REPLACE FUNCTION is_admin_user()
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN COALESCE((auth.jwt() ->> 'role')::text = 'admin', false);
EXCEPTION
  WHEN OTHERS THEN
    RETURN false;
END;
$$;

CREATE OR REPLACE FUNCTION get_current_user_tenant_id()
RETURNS uuid
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN COALESCE((auth.jwt() ->> 'tenant_id')::uuid, null);
EXCEPTION
  WHEN OTHERS THEN
    RETURN null;
END;
$$;

CREATE OR REPLACE FUNCTION get_current_user_role()
RETURNS text
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN COALESCE((auth.jwt() ->> 'role')::text, 'student');
EXCEPTION
  WHEN OTHERS THEN
    RETURN 'student';
END;
$$;

-- منح الصلاحيات
GRANT EXECUTE ON FUNCTION is_admin_user() TO authenticated;
GRANT EXECUTE ON FUNCTION get_current_user_tenant_id() TO authenticated;
GRANT EXECUTE ON FUNCTION get_current_user_role() TO authenticated;

-- ===== 4. دالة إنشاء طالب جديد =====
-- Student creation function

CREATE OR REPLACE FUNCTION create_student_user(
  user_email text,
  user_password text,
  user_name text,
  user_tenant_id uuid,
  student_grade text DEFAULT 'Grade 1'
)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  new_user_id uuid;
  result json;
BEGIN
  -- التحقق من الصلاحيات
  IF NOT (is_admin_user() OR 
          (get_current_user_role() IN ('school_manager', 'supervisor') AND 
           get_current_user_tenant_id() = user_tenant_id)) THEN
    result := json_build_object(
      'success', false,
      'error', 'Access denied',
      'message', 'Insufficient permissions'
    );
    RETURN result;
  END IF;

  -- التحقق من البيانات
  IF user_email IS NULL OR user_email = '' THEN
    result := json_build_object(
      'success', false,
      'error', 'Email required',
      'message', 'Email cannot be empty'
    );
    RETURN result;
  END IF;

  -- التحقق من عدم وجود مستخدم بنفس البريد
  IF EXISTS (SELECT 1 FROM users WHERE email = user_email) THEN
    result := json_build_object(
      'success', false,
      'error', 'Email exists',
      'message', 'User with this email already exists'
    );
    RETURN result;
  END IF;

  -- إنشاء معرف جديد
  new_user_id := gen_random_uuid();
  
  -- إدراج المستخدم
  INSERT INTO users (
    id, email, name, role, tenant_id, is_active, created_at, updated_at
  ) VALUES (
    new_user_id, user_email, user_name, 'student', user_tenant_id, 
    true, now(), now()
  );
  
  -- إدراج الطالب
  INSERT INTO students (
    id, name, grade, tenant_id, is_active, created_at, updated_at
  ) VALUES (
    new_user_id, user_name, student_grade, user_tenant_id, 
    true, now(), now()
  );
  
  result := json_build_object(
    'success', true,
    'user_id', new_user_id,
    'message', 'Student created successfully'
  );
  
  RETURN result;
  
EXCEPTION
  WHEN OTHERS THEN
    result := json_build_object(
      'success', false,
      'error', SQLERRM,
      'message', 'Failed to create student'
    );
    RETURN result;
END;
$$;

-- ===== 5. دالة إنشاء مستخدم عادي =====
-- Regular user creation function

CREATE OR REPLACE FUNCTION create_regular_user(
  user_email text,
  user_password text,
  user_name text,
  user_role user_role,
  user_tenant_id uuid
)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  new_user_id uuid;
  result json;
BEGIN
  -- التحقق من الصلاحيات
  IF NOT (is_admin_user() OR 
          (get_current_user_role() = 'school_manager' AND 
           get_current_user_tenant_id() = user_tenant_id)) THEN
    result := json_build_object(
      'success', false,
      'error', 'Access denied',
      'message', 'Insufficient permissions'
    );
    RETURN result;
  END IF;

  -- التحقق من البيانات
  IF user_email IS NULL OR user_email = '' THEN
    result := json_build_object(
      'success', false,
      'error', 'Email required',
      'message', 'Email cannot be empty'
    );
    RETURN result;
  END IF;

  -- التحقق من عدم وجود مستخدم بنفس البريد
  IF EXISTS (SELECT 1 FROM users WHERE email = user_email) THEN
    result := json_build_object(
      'success', false,
      'error', 'Email exists',
      'message', 'User with this email already exists'
    );
    RETURN result;
  END IF;

  -- إنشاء معرف جديد
  new_user_id := gen_random_uuid();
  
  -- إدراج المستخدم
  INSERT INTO users (
    id, email, name, role, tenant_id, is_active, created_at, updated_at
  ) VALUES (
    new_user_id, user_email, user_name, user_role, user_tenant_id, 
    true, now(), now()
  );
  
  result := json_build_object(
    'success', true,
    'user_id', new_user_id,
    'message', 'User created successfully'
  );
  
  RETURN result;
  
EXCEPTION
  WHEN OTHERS THEN
    result := json_build_object(
      'success', false,
      'error', SQLERRM,
      'message', 'Failed to create user'
    );
    RETURN result;
END;
$$;

-- ===== 6. دوال RPC للأدمن =====
-- Admin RPC functions

CREATE OR REPLACE FUNCTION get_all_users()
RETURNS SETOF users
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  IF NOT is_admin_user() THEN
    RAISE EXCEPTION 'Access denied. Admin role required.';
  END IF;
  
  RETURN QUERY SELECT * FROM users ORDER BY created_at DESC;
END;
$$;

CREATE OR REPLACE FUNCTION get_all_students()
RETURNS SETOF students
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  IF NOT is_admin_user() THEN
    RAISE EXCEPTION 'Access denied. Admin role required.';
  END IF;
  
  RETURN QUERY SELECT * FROM students ORDER BY created_at DESC;
END;
$$;

CREATE OR REPLACE FUNCTION get_all_buses()
RETURNS SETOF buses
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  IF NOT is_admin_user() THEN
    RAISE EXCEPTION 'Access denied. Admin role required.';
  END IF;
  
  RETURN QUERY SELECT * FROM buses ORDER BY created_at DESC;
END;
$$;

CREATE OR REPLACE FUNCTION get_all_routes()
RETURNS SETOF routes
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  IF NOT is_admin_user() THEN
    RAISE EXCEPTION 'Access denied. Admin role required.';
  END IF;
  
  RETURN QUERY SELECT * FROM routes ORDER BY created_at DESC;
END;
$$;

CREATE OR REPLACE FUNCTION get_all_tenants()
RETURNS SETOF tenants
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  IF NOT is_admin_user() THEN
    RAISE EXCEPTION 'Access denied. Admin role required.';
  END IF;
  
  RETURN QUERY SELECT * FROM tenants ORDER BY created_at DESC;
END;
$$;

-- منح الصلاحيات للدوال
GRANT EXECUTE ON FUNCTION create_student_user(text, text, text, uuid, text) TO authenticated;
GRANT EXECUTE ON FUNCTION create_regular_user(text, text, text, user_role, uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION get_all_users() TO authenticated;
GRANT EXECUTE ON FUNCTION get_all_students() TO authenticated;
GRANT EXECUTE ON FUNCTION get_all_buses() TO authenticated;
GRANT EXECUTE ON FUNCTION get_all_routes() TO authenticated;
GRANT EXECUTE ON FUNCTION get_all_tenants() TO authenticated;
