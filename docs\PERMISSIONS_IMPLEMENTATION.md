# 🛡️ تطبيق الصلاحيات في النظام - دليل المطور

## نظرة عامة
هذا المستند يوضح كيفية تطبيق نظام الصلاحيات في الكود، بما في ذلك سياسات RLS وفحص الصلاحيات في Frontend.

---

## 🔒 Row Level Security (RLS) Policies

### سياسات المستخدمين (Users)
```sql
-- سياسة SELECT: عرض المستخدمين
CREATE POLICY "users_select_policy" ON public.users
FOR SELECT USING (
    -- المستخدم يمكنه رؤية بياناته الشخصية
    auth.uid() = id
    OR
    -- الأدمن يمكنه رؤية جميع المستخدمين
    (
        auth.jwt() ->> 'role' = 'admin'
        AND auth.jwt() ->> 'is_active' = 'true'
    )
    OR
    -- مدير المدرسة يمكنه رؤية مستخدمي مدرسته
    (
        auth.jwt() ->> 'role' = 'school_manager'
        AND auth.jwt() ->> 'is_active' = 'true'
        AND tenant_id = (auth.jwt() ->> 'tenant_id')::uuid
    )
    OR
    -- المشرف يمكنه رؤية مستخدمي مدرسته
    (
        auth.jwt() ->> 'role' = 'supervisor'
        AND auth.jwt() ->> 'is_active' = 'true'
        AND tenant_id = (auth.jwt() ->> 'tenant_id')::uuid
    )
);
```

### سياسات المدارس (Tenants)
```sql
-- سياسة SELECT: عرض المدارس
CREATE POLICY "tenants_select_policy" ON public.tenants
FOR SELECT USING (
    -- الأدمن يمكنه رؤية جميع المدارس
    EXISTS (
        SELECT 1 FROM public.users 
        WHERE id = auth.uid() 
        AND role = 'admin'
        AND is_active = true
    )
    OR
    -- مدير المدرسة والمشرف يمكنهما رؤية مدرستهما فقط
    EXISTS (
        SELECT 1 FROM public.users 
        WHERE id = auth.uid() 
        AND role IN ('school_manager', 'supervisor')
        AND tenant_id = tenants.id
        AND is_active = true
    )
);
```

### سياسات الطلاب (Students)
```sql
-- سياسة SELECT: عرض الطلاب
CREATE POLICY "students_select_policy" ON public.students
FOR SELECT USING (
    -- الأدمن يمكنه رؤية جميع الطلاب
    EXISTS (
        SELECT 1 FROM public.users 
        WHERE id = auth.uid() 
        AND role = 'admin'
        AND is_active = true
    )
    OR
    -- مدير المدرسة والمشرف يمكنهما رؤية طلاب مدرستهما
    EXISTS (
        SELECT 1 FROM public.users 
        WHERE id = auth.uid() 
        AND role IN ('school_manager', 'supervisor')
        AND tenant_id = students.tenant_id
        AND is_active = true
    )
    OR
    -- ولي الأمر يمكنه رؤية أطفاله فقط
    parent_id = auth.uid()
);
```

---

## ⚛️ Frontend Permissions

### استخدام usePermissions Hook
```typescript
import { usePermissions } from '../hooks/usePermissions';

const MyComponent = () => {
  const { 
    isAdmin, 
    isSchoolManager, 
    isSupervisor, 
    isDriver, 
    isParent,
    hasPermission 
  } = usePermissions();

  return (
    <div>
      {/* عرض للأدمن فقط */}
      {isAdmin && (
        <AdminPanel />
      )}

      {/* عرض لمدير المدرسة والأدمن */}
      {(isAdmin || isSchoolManager) && (
        <SchoolManagement />
      )}

      {/* عرض للجميع عدا ولي الأمر */}
      {!isParent && (
        <StaffDashboard />
      )}
    </div>
  );
};
```

### مكون PermissionGuard
```typescript
import { PermissionGuard } from '../components/auth/PermissionGuard';
import { Permission } from '../lib/rbac';

const SchoolsPage = () => {
  return (
    <div>
      {/* زر إضافة مدرسة - للأدمن فقط */}
      <PermissionGuard permission={Permission.TENANTS_CREATE}>
        <Button onClick={handleAddSchool}>
          إضافة مدرسة
        </Button>
      </PermissionGuard>

      {/* زر تعديل - للأدمن ومدير المدرسة */}
      <PermissionGuard permission={Permission.TENANTS_EDIT}>
        <Button onClick={handleEditSchool}>
          تعديل
        </Button>
      </PermissionGuard>

      {/* زر حذف - للأدمن فقط */}
      <PermissionGuard permission={Permission.TENANTS_DELETE}>
        <Button onClick={handleDeleteSchool}>
          حذف
        </Button>
      </PermissionGuard>
    </div>
  );
};
```

### تصفية البيانات حسب الدور
```typescript
const useFilteredData = () => {
  const { user } = useAuth();
  const { isAdmin } = usePermissions();
  const { tenants, users, students } = useDatabase();

  // تصفية المدارس
  const filteredTenants = useMemo(() => {
    if (isAdmin) {
      return tenants; // الأدمن يرى جميع المدارس
    }
    
    if (user?.tenant_id) {
      return tenants.filter(tenant => tenant.id === user.tenant_id);
    }
    
    return [];
  }, [tenants, user, isAdmin]);

  // تصفية المستخدمين
  const filteredUsers = useMemo(() => {
    if (isAdmin) {
      return users; // الأدمن يرى جميع المستخدمين
    }
    
    if (user?.tenant_id) {
      return users.filter(u => u.tenant_id === user.tenant_id);
    }
    
    return [];
  }, [users, user, isAdmin]);

  return {
    filteredTenants,
    filteredUsers,
    // ... باقي البيانات المفلترة
  };
};
```

---

## 🔐 API Security

### فحص الصلاحيات في API Calls
```typescript
// في دالة API
const deleteSchool = async (schoolId: string) => {
  // فحص الصلاحيات قبل الحذف
  if (!isAdmin) {
    throw new Error('ليس لديك صلاحية لحذف المدارس');
  }

  // استخدام دالة الحذف الآمن
  const { data, error } = await supabase.rpc('safe_delete_tenant', {
    tenant_id_to_delete: schoolId
  });

  if (error) throw error;
  return data;
};
```

### معالجة أخطاء الصلاحيات
```typescript
const handleApiError = (error: any) => {
  if (error?.code === '42501') {
    // خطأ RLS - عدم وجود صلاحية
    alert('ليس لديك صلاحية لتنفيذ هذا الإجراء');
  } else if (error?.message?.includes('permission')) {
    // خطأ صلاحيات عام
    alert('خطأ في الصلاحيات: ' + error.message);
  } else {
    // خطأ عام
    alert('حدث خطأ غير متوقع');
  }
};
```

---

## 📊 تطبيق الصلاحيات في الإحصائيات

### تصفية الإحصائيات حسب الدور
```typescript
const useStatsData = () => {
  const { user } = useAuth();
  const { isAdmin, isSchoolManager } = usePermissions();

  const fetchStats = async () => {
    let query = supabase.from('students').select('*');

    // تطبيق فلترة حسب الدور
    if (!isAdmin && user?.tenant_id) {
      query = query.eq('tenant_id', user.tenant_id);
    }

    const { data, error } = await query;
    
    if (error) throw error;
    return data;
  };

  return { fetchStats };
};
```

### عرض الإحصائيات المناسبة
```typescript
const StatsComponent = () => {
  const { isAdmin, isSchoolManager, isSupervisor } = usePermissions();

  return (
    <div>
      {/* إحصائيات للأدمن */}
      {isAdmin && (
        <AdminStats />
      )}

      {/* إحصائيات لمدير المدرسة */}
      {isSchoolManager && (
        <SchoolManagerStats />
      )}

      {/* إحصائيات للمشرف */}
      {isSupervisor && (
        <SupervisorStats />
      )}
    </div>
  );
};
```

---

## 🛠️ دوال مساعدة للصلاحيات

### فحص صلاحية محددة
```typescript
const checkPermission = (
  userRole: string, 
  requiredPermission: string,
  resourceTenantId?: string,
  userTenantId?: string
): boolean => {
  switch (userRole) {
    case 'admin':
      return true; // الأدمن له جميع الصلاحيات

    case 'school_manager':
      // مدير المدرسة له صلاحيات في مدرسته فقط
      if (resourceTenantId && userTenantId) {
        return resourceTenantId === userTenantId;
      }
      return false;

    case 'supervisor':
      // المشرف له صلاحيات قراءة فقط في مدرسته
      if (requiredPermission.includes('read') || requiredPermission.includes('view')) {
        if (resourceTenantId && userTenantId) {
          return resourceTenantId === userTenantId;
        }
      }
      return false;

    default:
      return false;
  }
};
```

### فحص ملكية المورد
```typescript
const canAccessResource = (
  resource: any,
  user: any,
  action: 'read' | 'write' | 'delete'
): boolean => {
  // الأدمن يمكنه الوصول لكل شيء
  if (user.role === 'admin') {
    return true;
  }

  // فحص ملكية المورد
  if (resource.tenant_id && user.tenant_id) {
    const sameSchool = resource.tenant_id === user.tenant_id;
    
    switch (user.role) {
      case 'school_manager':
        return sameSchool;
        
      case 'supervisor':
        return sameSchool && action === 'read';
        
      case 'driver':
        return sameSchool && (
          resource.driver_id === user.id || 
          action === 'read'
        );
        
      case 'parent':
        return resource.parent_id === user.id;
        
      default:
        return false;
    }
  }

  return false;
};
```

---

## 📝 أفضل الممارسات

### 1. دفاع متعدد الطبقات
- فحص الصلاحيات في Frontend
- تطبيق RLS في قاعدة البيانات
- التحقق في API calls

### 2. مبدأ الحد الأدنى من الصلاحيات
- إعطاء أقل صلاحيات ممكنة
- فحص الصلاحيات قبل كل عملية
- تسجيل جميع العمليات الحساسة

### 3. معالجة الأخطاء
- رسائل خطأ واضحة ومفيدة
- عدم كشف معلومات حساسة في الأخطاء
- تسجيل محاولات الوصول غير المصرح بها

### 4. اختبار الصلاحيات
- اختبار كل دور على حدة
- اختبار محاولات الوصول غير المصرح بها
- اختبار تغيير الأدوار

---

**تاريخ آخر تحديث:** ديسمبر 2024  
**الإصدار:** 1.0  
**المطور:** نظام إدارة الحافلات المدرسية
