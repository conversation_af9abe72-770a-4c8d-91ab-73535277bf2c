# 🧪 دليل اختبار والتحقق من المرحلة الأولى
# Phase 1 Testing & Verification Guide

هذا الدليل يوضح كيفية اختبار والتحقق من أن النظام الأمني الجديد يعمل بشكل صحيح.

---

## 🔍 **1. فحص سلامة النظام**

### **فحص الحالة العامة:**
```sql
-- فحص سلامة النظام
SELECT public.check_permission_system_integrity();

-- عرض مراقبة النظام
SELECT * FROM public.permission_system_status;
```

### **النتيجة المتوقعة:**
- `system_status`: "HEALTHY"
- `function_count`: 4
- `matrix_entries`: 34+
- `policy_count`: 29+

---

## 🔐 **2. اختب<PERSON>ر الدوال الأساسية**

### **اختبار دالة فحص الأدمن:**
```sql
-- اختبار مع مستخدم أدمن
SELECT public.is_system_admin('USER_ID_HERE');
-- النتيجة المتوقعة: true للأدمن، false للآخرين

-- اختبار الحصول على الدور
SELECT public.get_user_role_secure('USER_ID_HERE');
-- النتيجة المتوقعة: 'admin', 'school_manager', إلخ

-- اختبار الحصول على المستأجر
SELECT public.get_user_tenant_secure('USER_ID_HERE');
-- النتيجة المتوقعة: UUID للمستأجر أو null للأدمن
```

### **اختبار دالة فحص الصلاحيات:**
```sql
-- اختبار صلاحية قراءة المستخدمين للأدمن
SELECT public.check_permission(
  'ADMIN_USER_ID',
  'user',
  'read',
  null,
  null,
  '{}'::jsonb
);
-- النتيجة المتوقعة: true

-- اختبار صلاحية حذف المستخدمين لمدير المدرسة
SELECT public.check_permission(
  'SCHOOL_MANAGER_ID',
  'user',
  'delete',
  'TENANT_ID',
  'TARGET_USER_ID',
  '{"resource_id": "TARGET_USER_ID"}'::jsonb
);
-- النتيجة المتوقعة: true إذا كان في نفس المستأجر، false إذا لم يكن
```

---

## 👥 **3. اختبار الصلاحيات حسب الدور**

### **اختبار صلاحيات الأدمن:**
```sql
-- يجب أن يكون للأدمن وصول لجميع البيانات
SELECT COUNT(*) FROM users; -- يجب أن يرى جميع المستخدمين
SELECT COUNT(*) FROM tenants; -- يجب أن يرى جميع المدارس
SELECT COUNT(*) FROM buses; -- يجب أن يرى جميع الحافلات
```

### **اختبار صلاحيات مدير المدرسة:**
```sql
-- يجب أن يرى بيانات مدرسته فقط
SET LOCAL "request.jwt.claims" = '{"sub": "SCHOOL_MANAGER_ID"}';
SELECT COUNT(*) FROM users; -- يجب أن يرى مستخدمي مدرسته فقط
SELECT COUNT(*) FROM buses; -- يجب أن يرى حافلات مدرسته فقط
```

### **اختبار صلاحيات السائق:**
```sql
-- يجب أن يرى الحافلة المخصصة له فقط
SET LOCAL "request.jwt.claims" = '{"sub": "DRIVER_ID"}';
SELECT COUNT(*) FROM buses WHERE driver_id = 'DRIVER_ID'; -- يجب أن يرى حافلته فقط
```

---

## 🛡️ **4. اختبار عزل البيانات**

### **اختبار عزل المستأجرين:**
```sql
-- تسجيل الدخول كمدير مدرسة A
SET LOCAL "request.jwt.claims" = '{"sub": "SCHOOL_MANAGER_A_ID"}';
SELECT COUNT(*) FROM students WHERE tenant_id = 'TENANT_A_ID'; -- يجب أن يرى طلاب مدرسته

-- محاولة رؤية طلاب مدرسة أخرى
SELECT COUNT(*) FROM students WHERE tenant_id = 'TENANT_B_ID'; -- يجب أن يكون 0
```

### **اختبار حماية البيانات الحساسة:**
```sql
-- محاولة وصول غير مصرح به
SET LOCAL "request.jwt.claims" = '{"sub": "STUDENT_ID"}';
SELECT COUNT(*) FROM users WHERE role = 'admin'; -- يجب أن يكون 0
```

---

## 📊 **5. اختبار الأداء**

### **قياس سرعة فحص الصلاحيات:**
```sql
-- اختبار سرعة الاستجابة
\timing on
SELECT public.check_permission(
  'USER_ID',
  'user',
  'read',
  'TENANT_ID',
  'TARGET_ID',
  '{}'::jsonb
);
\timing off
-- يجب أن يكون أقل من 10ms
```

### **اختبار الحمولة:**
```sql
-- اختبار 100 فحص صلاحية
DO $$
DECLARE
  i INTEGER;
  start_time TIMESTAMP;
  end_time TIMESTAMP;
BEGIN
  start_time := clock_timestamp();
  
  FOR i IN 1..100 LOOP
    PERFORM public.check_permission(
      'USER_ID',
      'user',
      'read',
      'TENANT_ID',
      'TARGET_ID',
      '{}'::jsonb
    );
  END LOOP;
  
  end_time := clock_timestamp();
  RAISE NOTICE 'Time for 100 checks: %', end_time - start_time;
END $$;
-- يجب أن يكون أقل من 1 ثانية
```

---

## 📝 **6. اختبار تسجيل الأحداث**

### **اختبار تسجيل الأحداث الأمنية:**
```sql
-- تسجيل حدث تجريبي
SELECT public.log_security_event(
  'TEST_EVENT',
  'low',
  'This is a test security event',
  auth.uid(),
  null,
  '{"test": true}'::jsonb
);

-- التحقق من التسجيل
SELECT * FROM security_events 
WHERE event_type = 'TEST_EVENT' 
ORDER BY created_at DESC 
LIMIT 1;
```

### **اختبار تسجيل تغييرات الصلاحيات:**
```sql
-- تسجيل تغيير تجريبي
SELECT public.log_permission_change(
  'PERMISSION_GRANTED',
  'user',
  'read',
  null,
  '{"granted": true}'::jsonb,
  'Test permission change'
);

-- التحقق من التسجيل
SELECT * FROM permission_change_log 
WHERE change_type = 'PERMISSION_GRANTED' 
ORDER BY created_at DESC 
LIMIT 1;
```

---

## ⚠️ **7. اختبار حالات الخطأ**

### **اختبار الصلاحيات المرفوضة:**
```sql
-- محاولة وصول غير مصرح به
SELECT public.check_permission(
  'STUDENT_ID',
  'user',
  'delete',
  'TENANT_ID',
  'ADMIN_ID',
  '{}'::jsonb
);
-- النتيجة المتوقعة: false

-- التحقق من تسجيل الحدث الأمني
SELECT * FROM security_events 
WHERE event_type = 'PERMISSION_DENIED' 
ORDER BY created_at DESC 
LIMIT 1;
```

### **اختبار المعاملات الخاطئة:**
```sql
-- اختبار مع معاملات null
SELECT public.check_permission(null, 'user', 'read', null, null, null);
-- النتيجة المتوقعة: false

-- اختبار مع نوع مورد غير موجود
SELECT public.check_permission('USER_ID', 'invalid_resource', 'read', null, null, null);
-- النتيجة المتوقعة: false
```

---

## ✅ **8. قائمة التحقق النهائية**

### **يجب التأكد من:**
- [ ] جميع الدوال الأساسية تعمل
- [ ] مصفوفة الصلاحيات مكتملة
- [ ] السياسات مطبقة على جميع الجداول
- [ ] عزل البيانات يعمل بشكل صحيح
- [ ] تسجيل الأحداث يعمل
- [ ] الأداء مقبول (< 10ms لكل فحص)
- [ ] حالات الخطأ تُعامل بشكل صحيح
- [ ] لا توجد أخطاء في السجلات

### **أوامر التحقق السريع:**
```sql
-- فحص شامل سريع
SELECT 
  'System Status' as check,
  (public.check_permission_system_integrity()->>'system_status') as result;

-- عدد السياسات
SELECT 'Policies Count' as check, COUNT(*) as result 
FROM pg_policies WHERE schemaname = 'public';

-- عدد الصلاحيات
SELECT 'Permissions Count' as check, COUNT(*) as result 
FROM permission_matrix WHERE is_active = true;

-- آخر الأحداث الأمنية
SELECT 'Recent Events' as check, COUNT(*) as result 
FROM security_events WHERE created_at > now() - interval '1 hour';
```

---

**🎯 إذا نجحت جميع الاختبارات، فإن النظام جاهز للاستخدام في الإنتاج! ✅**
