-- إدراج بيانات تجريبية لاختبار النظام
-- Sample Data for Testing the School Bus Management System

-- 1. إدراج حافلات تجريبية (إذا لم تكن موجودة)
-- Insert sample buses (if not exists)
INSERT INTO buses (id, plate_number, model, capacity, driver_name, tenant_id, created_at, updated_at) VALUES
('432be8dd-eb89-411b-bfee-a408936f6dce', 'ABC-123', 'Mercedes Sprinter', 30, 'أحمد محمد', '00000000-0000-0000-0000-000000000001', NOW(), NOW()),
('532be8dd-eb89-411b-bfee-a408936f6dce', 'XYZ-456', 'Toyota Coaster', 25, 'محمد علي', '00000000-0000-0000-0000-000000000001', NOW(), NOW()),
('632be8dd-eb89-411b-bfee-a408936f6dce', 'DEF-789', 'Hyundai County', 35, 'علي أحمد', '00000000-0000-0000-0000-000000000001', NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
  plate_number = EXCLUDED.plate_number,
  model = EXCLUDED.model,
  capacity = EXCLUDED.capacity,
  driver_name = EXCLUDED.driver_name,
  updated_at = NOW();

-- 2. إدراج مواقع تجريبية للحافلات
-- Insert sample bus locations
INSERT INTO bus_locations (bus_id, latitude, longitude, speed, heading, accuracy, tenant_id, timestamp) VALUES
('432be8dd-eb89-411b-bfee-a408936f6dce', 24.7136, 46.6753, 45.5, 90.0, 5.0, '00000000-0000-0000-0000-000000000001', NOW()),
('532be8dd-eb89-411b-bfee-a408936f6dce', 24.7200, 46.6800, 30.2, 180.0, 3.0, '00000000-0000-0000-0000-000000000001', NOW()),
('632be8dd-eb89-411b-bfee-a408936f6dce', 24.7300, 46.6900, 0.0, 0.0, 2.0, '00000000-0000-0000-0000-000000000001', NOW());

-- 3. إدراج مسارات تجريبية
-- Insert sample routes
INSERT INTO routes (id, name, description, bus_id, tenant_id, created_at, updated_at) VALUES
('route-001', 'المسار الشمالي', 'مسار يخدم الأحياء الشمالية', '432be8dd-eb89-411b-bfee-a408936f6dce', '00000000-0000-0000-0000-000000000001', NOW(), NOW()),
('route-002', 'المسار الجنوبي', 'مسار يخدم الأحياء الجنوبية', '532be8dd-eb89-411b-bfee-a408936f6dce', '00000000-0000-0000-0000-000000000001', NOW(), NOW()),
('route-003', 'المسار الشرقي', 'مسار يخدم الأحياء الشرقية', '632be8dd-eb89-411b-bfee-a408936f6dce', '00000000-0000-0000-0000-000000000001', NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  description = EXCLUDED.description,
  bus_id = EXCLUDED.bus_id,
  updated_at = NOW();

-- 4. إدراج محطات توقف تجريبية
-- Insert sample bus stops
INSERT INTO bus_stops (id, name, latitude, longitude, address, tenant_id, created_at, updated_at) VALUES
('stop-001', 'محطة المدرسة الرئيسية', 24.7136, 46.6753, 'المدرسة الرئيسية، الرياض', '00000000-0000-0000-0000-000000000001', NOW(), NOW()),
('stop-002', 'حي النرجس', 24.7200, 46.6800, 'حي النرجس، الرياض', '00000000-0000-0000-0000-000000000001', NOW(), NOW()),
('stop-003', 'حي الملقا', 24.7300, 46.6900, 'حي الملقا، الرياض', '00000000-0000-0000-0000-000000000001', NOW(), NOW()),
('stop-004', 'حي العليا', 24.7100, 46.6700, 'حي العليا، الرياض', '00000000-0000-0000-0000-000000000001', NOW(), NOW()),
('stop-005', 'حي الصحافة', 24.7400, 46.7000, 'حي الصحافة، الرياض', '00000000-0000-0000-0000-000000000001', NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  latitude = EXCLUDED.latitude,
  longitude = EXCLUDED.longitude,
  address = EXCLUDED.address,
  updated_at = NOW();

-- 5. ربط المسارات بمحطات التوقف
-- Link routes with bus stops
INSERT INTO route_stops (route_id, stop_id, stop_order, estimated_arrival_time, tenant_id) VALUES
-- المسار الشمالي
('route-001', 'stop-001', 1, '07:00:00', '00000000-0000-0000-0000-000000000001'),
('route-001', 'stop-002', 2, '07:15:00', '00000000-0000-0000-0000-000000000001'),
('route-001', 'stop-003', 3, '07:30:00', '00000000-0000-0000-0000-000000000001'),

-- المسار الجنوبي
('route-002', 'stop-001', 1, '07:00:00', '00000000-0000-0000-0000-000000000001'),
('route-002', 'stop-004', 2, '07:20:00', '00000000-0000-0000-0000-000000000001'),

-- المسار الشرقي
('route-003', 'stop-001', 1, '07:00:00', '00000000-0000-0000-0000-000000000001'),
('route-003', 'stop-005', 2, '07:25:00', '00000000-0000-0000-0000-000000000001')
ON CONFLICT DO NOTHING;

-- 6. إدراج بيانات صيانة تجريبية
-- Insert sample maintenance data
INSERT INTO bus_maintenance (id, bus_id, maintenance_type, description, status, scheduled_date, cost, tenant_id, created_at, updated_at) VALUES
('maint-001', '432be8dd-eb89-411b-bfee-a408936f6dce', 'تغيير زيت المحرك', 'تغيير زيت المحرك والفلتر', 'scheduled', NOW() + INTERVAL '7 days', 150.00, '00000000-0000-0000-0000-000000000001', NOW(), NOW()),
('maint-002', '532be8dd-eb89-411b-bfee-a408936f6dce', 'فحص الفرامل', 'فحص وصيانة نظام الفرامل', 'scheduled', NOW() + INTERVAL '3 days', 300.00, '00000000-0000-0000-0000-000000000001', NOW(), NOW()),
('maint-003', '632be8dd-eb89-411b-bfee-a408936f6dce', 'تغيير الإطارات', 'تغيير الإطارات الأمامية', 'completed', NOW() - INTERVAL '5 days', 800.00, '00000000-0000-0000-0000-000000000001', NOW(), NOW()),
('maint-004', '432be8dd-eb89-411b-bfee-a408936f6dce', 'صيانة دورية', 'صيانة دورية شاملة', 'scheduled', NOW() - INTERVAL '2 days', 500.00, '00000000-0000-0000-0000-000000000001', NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
  maintenance_type = EXCLUDED.maintenance_type,
  description = EXCLUDED.description,
  status = EXCLUDED.status,
  scheduled_date = EXCLUDED.scheduled_date,
  cost = EXCLUDED.cost,
  updated_at = NOW();

-- 7. تحديث آخر موقع للحافلات
-- Update last location for buses
UPDATE buses SET 
  last_location = jsonb_build_object(
    'latitude', bl.latitude,
    'longitude', bl.longitude,
    'speed', bl.speed,
    'heading', bl.heading,
    'timestamp', bl.timestamp
  ),
  last_updated = bl.timestamp,
  current_students = CASE 
    WHEN plate_number = 'ABC-123' THEN 25
    WHEN plate_number = 'XYZ-456' THEN 18
    WHEN plate_number = 'DEF-789' THEN 0
    ELSE 0
  END
FROM (
  SELECT DISTINCT ON (bus_id) 
    bus_id, latitude, longitude, speed, heading, timestamp
  FROM bus_locations 
  ORDER BY bus_id, timestamp DESC
) bl
WHERE buses.id = bl.bus_id;

-- 8. إنشاء مؤشرات إضافية للأداء
-- Create additional performance indexes
CREATE INDEX IF NOT EXISTS idx_buses_tenant_id ON buses(tenant_id);
CREATE INDEX IF NOT EXISTS idx_buses_plate_number ON buses(plate_number);
CREATE INDEX IF NOT EXISTS idx_routes_tenant_id_active ON routes(tenant_id, is_active);

-- 9. إنشاء view محسن للحافلات مع المواقع
-- Create enhanced view for buses with locations
CREATE OR REPLACE VIEW buses_with_latest_location AS
SELECT 
    b.*,
    bl.latitude,
    bl.longitude,
    bl.speed,
    bl.heading,
    bl.accuracy,
    bl.timestamp as location_timestamp,
    r.name as route_name,
    r.id as route_id
FROM buses b
LEFT JOIN LATERAL (
    SELECT latitude, longitude, speed, heading, accuracy, timestamp
    FROM bus_locations
    WHERE bus_id = b.id
    ORDER BY timestamp DESC
    LIMIT 1
) bl ON true
LEFT JOIN routes r ON r.bus_id = b.id AND r.is_active = true;

-- 10. إنشاء view للصيانة مع تفاصيل الحافلة
-- Create maintenance view with bus details
CREATE OR REPLACE VIEW maintenance_with_bus_details AS
SELECT 
    bm.*,
    b.plate_number,
    b.model,
    b.driver_name,
    CASE 
        WHEN bm.status = 'scheduled' AND bm.scheduled_date < NOW() THEN 'overdue'
        ELSE bm.status
    END as actual_status,
    CASE 
        WHEN bm.status = 'scheduled' AND bm.scheduled_date < NOW() THEN 
            EXTRACT(DAY FROM (NOW() - bm.scheduled_date))::INTEGER
        ELSE 0
    END as days_overdue
FROM bus_maintenance bm
JOIN buses b ON bm.bus_id = b.id;

-- 11. منح الصلاحيات على الـ views الجديدة
-- Grant permissions on new views
GRANT SELECT ON buses_with_latest_location TO authenticated;
GRANT SELECT ON maintenance_with_bus_details TO authenticated;

-- 12. إنشاء دالة للحصول على إحصائيات الصيانة
-- Create function to get maintenance statistics
CREATE OR REPLACE FUNCTION get_maintenance_stats(tenant_uuid UUID)
RETURNS TABLE(
    total_maintenance BIGINT,
    scheduled_maintenance BIGINT,
    in_progress_maintenance BIGINT,
    completed_maintenance BIGINT,
    overdue_maintenance BIGINT,
    total_cost DECIMAL,
    average_cost DECIMAL
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*) as total_maintenance,
        COUNT(*) FILTER (WHERE status = 'scheduled') as scheduled_maintenance,
        COUNT(*) FILTER (WHERE status = 'in_progress') as in_progress_maintenance,
        COUNT(*) FILTER (WHERE status = 'completed') as completed_maintenance,
        COUNT(*) FILTER (WHERE status = 'scheduled' AND scheduled_date < NOW()) as overdue_maintenance,
        COALESCE(SUM(cost), 0) as total_cost,
        COALESCE(AVG(cost), 0) as average_cost
    FROM bus_maintenance
    WHERE tenant_id = tenant_uuid;
END;
$$;

-- منح صلاحيات على الدالة الجديدة
GRANT EXECUTE ON FUNCTION get_maintenance_stats(UUID) TO authenticated;

-- 13. تحديث إعدادات RLS للجداول الجديدة
-- Update RLS settings for new tables

-- سياسات للصيانة
DROP POLICY IF EXISTS "Users can manage maintenance for their tenant" ON bus_maintenance;
CREATE POLICY "Users can manage maintenance for their tenant" ON bus_maintenance
    FOR ALL USING (tenant_id = (auth.jwt() ->> 'tenant_id')::uuid);

-- سياسات للمواقع
DROP POLICY IF EXISTS "Users can manage locations for their tenant" ON bus_locations;
CREATE POLICY "Users can manage locations for their tenant" ON bus_locations
    FOR ALL USING (tenant_id = (auth.jwt() ->> 'tenant_id')::uuid);

-- سياسات للمسارات
DROP POLICY IF EXISTS "Users can manage routes for their tenant" ON routes;
CREATE POLICY "Users can manage routes for their tenant" ON routes
    FOR ALL USING (tenant_id = (auth.jwt() ->> 'tenant_id')::uuid);

-- سياسات لمحطات التوقف
DROP POLICY IF EXISTS "Users can manage stops for their tenant" ON bus_stops;
CREATE POLICY "Users can manage stops for their tenant" ON bus_stops
    FOR ALL USING (tenant_id = (auth.jwt() ->> 'tenant_id')::uuid);

-- سياسات لربط المسارات بالمحطات
DROP POLICY IF EXISTS "Users can manage route stops for their tenant" ON route_stops;
CREATE POLICY "Users can manage route stops for their tenant" ON route_stops
    FOR ALL USING (tenant_id = (auth.jwt() ->> 'tenant_id')::uuid);

-- 14. إنشاء فهارس مركبة للاستعلامات المعقدة
-- Create composite indexes for complex queries
CREATE INDEX IF NOT EXISTS idx_bus_maintenance_tenant_status_date ON bus_maintenance(tenant_id, status, scheduled_date);
CREATE INDEX IF NOT EXISTS idx_bus_locations_bus_timestamp ON bus_locations(bus_id, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_route_stops_route_order ON route_stops(route_id, stop_order);

-- تم الانتهاء من إعداد قاعدة البيانات
-- Database setup completed
