-- Phase 1: Security Enhancement Migration
-- المرحلة الأولى: تحسين الأمان وإضافة جداول الحماية المتقدمة
-- Generated: 2025-01-30

-- ===== 1. إنشاء جداول الأمان الجديدة =====

-- جدول سجل محاولات تسجيل الدخول
CREATE TABLE IF NOT EXISTS public.login_attempts (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  email text NOT NULL,
  ip_address inet,
  user_agent text,
  success boolean NOT NULL DEFAULT false,
  failure_reason text,
  location_data jsonb DEFAULT '{}'::jsonb,
  device_info jsonb DEFAULT '{}'::jsonb,
  attempted_at timestamptz DEFAULT now(),
  
  -- فهارس للأداء
  CONSTRAINT login_attempts_email_check CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$')
);

-- جدول إعدادات التحقق الثنائي
CREATE TABLE IF NOT EXISTS public.user_2fa_settings (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  is_enabled boolean DEFAULT false,
  method text NOT NULL DEFAULT 'email' CHECK (method IN ('email', 'sms', 'totp')),
  secret_key text, -- للـ TOTP
  backup_codes text[], -- رموز احتياطية
  phone_number text, -- للـ SMS
  verified_at timestamptz,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  
  UNIQUE(user_id, method)
);

-- جدول الجلسات النشطة
CREATE TABLE IF NOT EXISTS public.active_sessions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  session_token text NOT NULL UNIQUE,
  refresh_token text,
  ip_address inet,
  user_agent text,
  device_info jsonb DEFAULT '{}'::jsonb,
  location_data jsonb DEFAULT '{}'::jsonb,
  is_active boolean DEFAULT true,
  last_activity timestamptz DEFAULT now(),
  expires_at timestamptz NOT NULL,
  created_at timestamptz DEFAULT now()
);

-- جدول سجل العمليات الأمنية (Audit Log)
CREATE TABLE IF NOT EXISTS public.security_audit_logs (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) ON DELETE SET NULL,
  tenant_id uuid REFERENCES public.tenants(id) ON DELETE SET NULL,
  action text NOT NULL,
  resource_type text,
  resource_id uuid,
  old_values jsonb DEFAULT '{}'::jsonb,
  new_values jsonb DEFAULT '{}'::jsonb,
  ip_address inet,
  user_agent text,
  risk_score integer DEFAULT 0 CHECK (risk_score >= 0 AND risk_score <= 100),
  severity text DEFAULT 'low' CHECK (severity IN ('low', 'medium', 'high', 'critical')),
  metadata jsonb DEFAULT '{}'::jsonb,
  timestamp timestamptz DEFAULT now()
);

-- جدول قيود الأمان
CREATE TABLE IF NOT EXISTS public.security_restrictions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
  restriction_type text NOT NULL CHECK (restriction_type IN ('ip_whitelist', 'time_restriction', 'location_restriction', 'device_restriction')),
  restriction_data jsonb NOT NULL DEFAULT '{}'::jsonb,
  is_active boolean DEFAULT true,
  created_by uuid REFERENCES auth.users(id),
  created_at timestamptz DEFAULT now(),
  expires_at timestamptz
);

-- ===== 2. إنشاء الفهارس للأداء =====

-- فهارس جدول محاولات تسجيل الدخول
CREATE INDEX IF NOT EXISTS idx_login_attempts_email_time 
ON public.login_attempts(email, attempted_at DESC);

CREATE INDEX IF NOT EXISTS idx_login_attempts_ip_time 
ON public.login_attempts(ip_address, attempted_at DESC);

CREATE INDEX IF NOT EXISTS idx_login_attempts_success_time 
ON public.login_attempts(success, attempted_at DESC);

-- فهارس جدول الجلسات النشطة
CREATE INDEX IF NOT EXISTS idx_active_sessions_user_id 
ON public.active_sessions(user_id, is_active);

CREATE INDEX IF NOT EXISTS idx_active_sessions_token 
ON public.active_sessions(session_token) WHERE is_active = true;

CREATE INDEX IF NOT EXISTS idx_active_sessions_expires 
ON public.active_sessions(expires_at) WHERE is_active = true;

-- فهارس جدول سجل العمليات
CREATE INDEX IF NOT EXISTS idx_security_audit_user_time 
ON public.security_audit_logs(user_id, timestamp DESC);

CREATE INDEX IF NOT EXISTS idx_security_audit_tenant_time 
ON public.security_audit_logs(tenant_id, timestamp DESC);

CREATE INDEX IF NOT EXISTS idx_security_audit_action_time 
ON public.security_audit_logs(action, timestamp DESC);

CREATE INDEX IF NOT EXISTS idx_security_audit_risk_score 
ON public.security_audit_logs(risk_score DESC, timestamp DESC);

-- ===== 3. إنشاء الدوال المساعدة =====

-- دالة تنظيف الجلسات المنتهية الصلاحية
CREATE OR REPLACE FUNCTION public.cleanup_expired_sessions()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- حذف الجلسات المنتهية الصلاحية
  DELETE FROM public.active_sessions 
  WHERE expires_at < now() OR is_active = false;
  
  -- تنظيف محاولات تسجيل الدخول القديمة (أكثر من 30 يوم)
  DELETE FROM public.login_attempts 
  WHERE attempted_at < now() - interval '30 days';
  
  -- تنظيف سجل العمليات القديم (أكثر من 90 يوم للعمليات منخفضة المخاطر)
  DELETE FROM public.security_audit_logs 
  WHERE timestamp < now() - interval '90 days' 
  AND risk_score < 30 
  AND severity = 'low';
END;
$$;

-- دالة فحص محاولات تسجيل الدخول الفاشلة
CREATE OR REPLACE FUNCTION public.check_failed_login_attempts(
  p_email text,
  p_ip_address inet DEFAULT NULL,
  p_time_window interval DEFAULT '15 minutes'::interval,
  p_max_attempts integer DEFAULT 5
)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  email_attempts integer := 0;
  ip_attempts integer := 0;
  result jsonb;
BEGIN
  -- عدد المحاولات الفاشلة للبريد الإلكتروني
  SELECT COUNT(*) INTO email_attempts
  FROM public.login_attempts
  WHERE email = p_email
    AND success = false
    AND attempted_at > now() - p_time_window;
  
  -- عدد المحاولات الفاشلة لعنوان IP (إذا تم توفيره)
  IF p_ip_address IS NOT NULL THEN
    SELECT COUNT(*) INTO ip_attempts
    FROM public.login_attempts
    WHERE ip_address = p_ip_address
      AND success = false
      AND attempted_at > now() - p_time_window;
  END IF;
  
  -- إنشاء النتيجة
  result := jsonb_build_object(
    'email_attempts', email_attempts,
    'ip_attempts', ip_attempts,
    'email_blocked', email_attempts >= p_max_attempts,
    'ip_blocked', ip_attempts >= p_max_attempts,
    'blocked', (email_attempts >= p_max_attempts OR ip_attempts >= p_max_attempts),
    'remaining_attempts', GREATEST(0, p_max_attempts - GREATEST(email_attempts, ip_attempts)),
    'reset_time', now() + p_time_window
  );
  
  RETURN result;
END;
$$;

-- دالة تسجيل محاولة دخول
CREATE OR REPLACE FUNCTION public.log_login_attempt(
  p_email text,
  p_success boolean,
  p_ip_address inet DEFAULT NULL,
  p_user_agent text DEFAULT NULL,
  p_failure_reason text DEFAULT NULL,
  p_device_info jsonb DEFAULT '{}'::jsonb,
  p_location_data jsonb DEFAULT '{}'::jsonb
)
RETURNS uuid
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  attempt_id uuid;
BEGIN
  INSERT INTO public.login_attempts (
    email, success, ip_address, user_agent, failure_reason,
    device_info, location_data, attempted_at
  ) VALUES (
    p_email, p_success, p_ip_address, p_user_agent, p_failure_reason,
    p_device_info, p_location_data, now()
  ) RETURNING id INTO attempt_id;

  RETURN attempt_id;
END;
$$;

-- دالة تسجيل عملية أمنية
CREATE OR REPLACE FUNCTION public.log_security_event(
  p_user_id uuid,
  p_action text,
  p_resource_type text DEFAULT NULL,
  p_resource_id uuid DEFAULT NULL,
  p_old_values jsonb DEFAULT '{}'::jsonb,
  p_new_values jsonb DEFAULT '{}'::jsonb,
  p_ip_address inet DEFAULT NULL,
  p_user_agent text DEFAULT NULL,
  p_risk_score integer DEFAULT 0,
  p_severity text DEFAULT 'low',
  p_metadata jsonb DEFAULT '{}'::jsonb
)
RETURNS uuid
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  log_id uuid;
  user_tenant_id uuid;
BEGIN
  -- الحصول على tenant_id للمستخدم
  SELECT tenant_id INTO user_tenant_id
  FROM public.users
  WHERE id = p_user_id;

  INSERT INTO public.security_audit_logs (
    user_id, tenant_id, action, resource_type, resource_id,
    old_values, new_values, ip_address, user_agent,
    risk_score, severity, metadata, timestamp
  ) VALUES (
    p_user_id, user_tenant_id, p_action, p_resource_type, p_resource_id,
    p_old_values, p_new_values, p_ip_address, p_user_agent,
    p_risk_score, p_severity, p_metadata, now()
  ) RETURNING id INTO log_id;

  RETURN log_id;
END;
$$;

-- دالة إنشاء جلسة جديدة
CREATE OR REPLACE FUNCTION public.create_user_session(
  p_user_id uuid,
  p_session_token text,
  p_refresh_token text DEFAULT NULL,
  p_ip_address inet DEFAULT NULL,
  p_user_agent text DEFAULT NULL,
  p_device_info jsonb DEFAULT '{}'::jsonb,
  p_location_data jsonb DEFAULT '{}'::jsonb,
  p_expires_in interval DEFAULT '8 hours'::interval
)
RETURNS uuid
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  session_id uuid;
BEGIN
  INSERT INTO public.active_sessions (
    user_id, session_token, refresh_token, ip_address, user_agent,
    device_info, location_data, expires_at
  ) VALUES (
    p_user_id, p_session_token, p_refresh_token, p_ip_address, p_user_agent,
    p_device_info, p_location_data, now() + p_expires_in
  ) RETURNING id INTO session_id;

  RETURN session_id;
END;
$$;

-- ===== 4. تفعيل RLS على الجداول الجديدة =====

-- تفعيل RLS
ALTER TABLE public.login_attempts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_2fa_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.active_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.security_audit_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.security_restrictions ENABLE ROW LEVEL SECURITY;

-- ===== 5. إنشاء سياسات RLS =====

-- سياسات جدول محاولات تسجيل الدخول
CREATE POLICY "admin_login_attempts_access" ON public.login_attempts
  FOR ALL TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.users
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- سياسات جدول إعدادات 2FA
CREATE POLICY "users_own_2fa_settings" ON public.user_2fa_settings
  FOR ALL TO authenticated
  USING (user_id = auth.uid())
  WITH CHECK (user_id = auth.uid());

CREATE POLICY "admin_all_2fa_settings" ON public.user_2fa_settings
  FOR ALL TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.users
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- سياسات جدول الجلسات النشطة
CREATE POLICY "users_own_sessions" ON public.active_sessions
  FOR ALL TO authenticated
  USING (user_id = auth.uid())
  WITH CHECK (user_id = auth.uid());

CREATE POLICY "admin_all_sessions" ON public.active_sessions
  FOR ALL TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.users
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- سياسات جدول سجل العمليات الأمنية
CREATE POLICY "admin_security_audit_access" ON public.security_audit_logs
  FOR ALL TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.users
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

CREATE POLICY "users_own_audit_logs" ON public.security_audit_logs
  FOR SELECT TO authenticated
  USING (user_id = auth.uid());

-- سياسات جدول قيود الأمان
CREATE POLICY "admin_security_restrictions" ON public.security_restrictions
  FOR ALL TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.users
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

CREATE POLICY "users_own_restrictions" ON public.security_restrictions
  FOR SELECT TO authenticated
  USING (user_id = auth.uid());

-- ===== 6. منح الصلاحيات =====

-- منح صلاحيات تنفيذ الدوال
GRANT EXECUTE ON FUNCTION public.cleanup_expired_sessions() TO authenticated;
GRANT EXECUTE ON FUNCTION public.check_failed_login_attempts(text, inet, interval, integer) TO authenticated;
GRANT EXECUTE ON FUNCTION public.log_login_attempt(text, boolean, inet, text, text, jsonb, jsonb) TO authenticated;
GRANT EXECUTE ON FUNCTION public.log_security_event(uuid, text, text, uuid, jsonb, jsonb, inet, text, integer, text, jsonb) TO authenticated;
GRANT EXECUTE ON FUNCTION public.create_user_session(uuid, text, text, inet, text, jsonb, jsonb, interval) TO authenticated;

-- إنشاء مهمة تنظيف دورية (يتطلب تفعيل pg_cron)
-- SELECT cron.schedule('cleanup-expired-sessions', '0 */6 * * *', 'SELECT public.cleanup_expired_sessions();');

COMMENT ON TABLE public.login_attempts IS 'سجل محاولات تسجيل الدخول للمراقبة الأمنية';
COMMENT ON TABLE public.user_2fa_settings IS 'إعدادات التحقق الثنائي للمستخدمين';
COMMENT ON TABLE public.active_sessions IS 'الجلسات النشطة لإدارة تسجيل الدخول المتعدد';
COMMENT ON TABLE public.security_audit_logs IS 'سجل العمليات الأمنية والتدقيق';
COMMENT ON TABLE public.security_restrictions IS 'قيود الأمان المخصصة للمستخدمين';
