-- Enable pgcrypto extension for password hashing
CREATE EXTENSION IF NOT EXISTS pgcrypto;

-- Create a function that can create users with admin privileges
-- This function will be called from the client but executes with elevated privileges

CREATE OR REPLACE FUNCTION create_user_admin(
  user_email text,
  user_password text,
  user_name text,
  user_role text,
  user_tenant_id uuid
)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
DECLARE
  new_user_id uuid;
  result json;
BEGIN
  -- Generate a new UUID for the user
  new_user_id := gen_random_uuid();

  -- Insert into auth.users table directly (this requires SECURITY DEFINER)
  INSERT INTO auth.users (
    id,
    email,
    encrypted_password,
    email_confirmed_at,
    raw_app_meta_data,
    raw_user_meta_data,
    created_at,
    updated_at,
    role,
    aud,
    confirmation_token
  ) VALUES (
    new_user_id,
    user_email,
    crypt(user_password, gen_salt('bf')),
    now(), -- Auto-confirm email
    '{"provider":"email","providers":["email"]}',
    json_build_object('name', user_name, 'role', user_role),
    now(),
    now(),
    'authenticated',
    'authenticated',
    ''
  );

  -- Insert user record in public.users
  INSERT INTO users (
    id,
    email,
    name,
    role,
    tenant_id,
    is_active,
    created_at,
    updated_at
  ) VALUES (
    new_user_id,
    user_email,
    user_name,
    user_role::user_role,
    user_tenant_id,
    true,
    now(),
    now()
  );

  -- Return success result
  result := json_build_object(
    'success', true,
    'user_id', new_user_id,
    'message', 'User created successfully'
  );

  RETURN result;

EXCEPTION
  WHEN OTHERS THEN
    -- Return error result
    result := json_build_object(
      'success', false,
      'error', SQLERRM,
      'message', 'Failed to create user'
    );
    RETURN result;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION create_user_admin(text, text, text, text, uuid) TO authenticated;
