# 🎨 دليل إعداد نظام إدارة الثيمات

## 📋 المشكلة والحل

### المشكلة:
- لا تظهر خيارات الثيمات في القائمة الجانبية
- عدم وجود صلاحيات للوصول لصفحات الثيمات

### الحل المطبق:
تم إنشاء نظام شامل لإدارة الثيمات مع صلاحيات محددة لكل دور.

---

## 🔧 الملفات المضافة/المحدثة

### 1. **نظام الصلاحيات**
```
src/hooks/useThemePermissions.ts          ← صلاحيات الثيمات
src/services/security/ThemeSecurityService.ts ← أمان الثيمات
```

### 2. **مكونات الإدارة**
```
src/components/role-based/admin/ThemeManagement.tsx        ← لوحة الأدمن
src/components/role-based/school-manager/SchoolThemeSettings.tsx ← لوحة مدير المدرسة
src/components/features/theme-customization/ThemeCustomizer.tsx  ← أداة التخصيص
```

### 3. **الصفحات**
```
src/pages/admin/ThemeManagementPage.tsx   ← صفحة إدارة الثيمات للأدمن
src/pages/school/ThemeSettingsPage.tsx   ← صفحة إعدادات الثيم للمدرسة
```

### 4. **التوجيه والتكوين**
```
src/routes/ThemeRoutes.tsx               ← مسارات الثيمات
src/App.tsx                              ← إضافة المسارات الجديدة
src/lib/rbacCentralizedConfigEnhanced.ts ← إضافة صلاحيات الثيمات
```

### 5. **القائمة الجانبية**
```
src/components/layout/Sidebar.tsx        ← إضافة خيارات الثيمات
```

---

## 🚀 كيفية الاستخدام

### للأدمن (Super Admin):
1. **تسجيل الدخول** كأدمن
2. **في القائمة الجانبية** ← ستجد "إدارة الثيمات"
3. **الوصول**: `/admin/themes`
4. **الإمكانيات**:
   - عرض جميع المدارس وحالة ثيماتها
   - إنشاء ثيمات جديدة
   - تعديل الثيمات الموجودة
   - حذف الثيمات
   - معاينة الثيمات

### لمدير المدرسة (School Manager):
1. **تسجيل الدخول** كمدير مدرسة
2. **في القائمة الجانبية** ← ستجد "ثيم المدرسة"
3. **الوصول**: `/school/theme`
4. **الإمكانيات**:
   - تخصيص ألوان المدرسة
   - رفع شعار المدرسة
   - تغيير الخطوط
   - تعديل التخطيط
   - معاينة التغييرات

---

## 🔐 نظام الصلاحيات

### الأدوار والصلاحيات:

#### **Super Admin**:
- ✅ `canManageAllThemes`: إدارة جميع الثيمات
- ✅ `canDeleteThemes`: حذف الثيمات
- ✅ `canApproveThemes`: الموافقة على التغييرات
- ✅ جميع صلاحيات مدير المدرسة

#### **School Manager**:
- ✅ `canManageOwnTheme`: إدارة ثيم مدرسته فقط
- ✅ `canCustomizeColors`: تخصيص الألوان
- ✅ `canUploadLogo`: رفع الشعار
- ✅ `canChangeFonts`: تغيير الخطوط
- ✅ `canViewThemeSettings`: عرض الإعدادات

#### **Teacher/Driver/Parent**:
- ✅ `canViewThemeSettings`: عرض الإعدادات فقط (للمعلمين)
- ❌ باقي الأدوار: لا يمكنهم الوصول

---

## 🛠️ استكشاف الأخطاء

### المشكلة: لا تظهر خيارات الثيمات
**الحلول**:
1. **تأكد من الدور**: يجب أن يكون المستخدم Admin أو School Manager
2. **تحقق من الصلاحيات**:
   ```typescript
   import { useThemePermissions } from '../hooks/useThemePermissions';
   
   const permissions = useThemePermissions();
   console.log('Theme Permissions:', permissions);
   ```
3. **فحص القائمة الجانبية**:
   ```typescript
   import { useCanAccessThemes } from '../hooks/useThemePermissions';
   
   const canAccess = useCanAccessThemes();
   console.log('Can Access Themes:', canAccess);
   ```

### المشكلة: خطأ 404 عند الوصول للصفحات
**الحلول**:
1. **تأكد من إضافة المسارات** في `App.tsx`
2. **تحقق من الصلاحيات** في `rbacCentralizedConfigEnhanced.ts`
3. **فحص الـ Routes**:
   ```bash
   # تأكد من وجود الملفات
   ls src/pages/admin/ThemeManagementPage.tsx
   ls src/pages/school/ThemeSettingsPage.tsx
   ```

### المشكلة: خطأ في الاستيراد
**الحلول**:
1. **تأكد من وجود المكونات**:
   ```typescript
   // في App.tsx
   import ThemeManagementPage from "./pages/admin/ThemeManagementPage";
   import ThemeSettingsPage from "./pages/school/ThemeSettingsPage";
   ```
2. **تحقق من المسارات النسبية**
3. **أعد تشغيل الخادم**:
   ```bash
   npm run dev
   ```

---

## 📝 مثال كامل للاستخدام

### 1. فحص الصلاحيات:
```typescript
import { useThemePermissions } from '../hooks/useThemePermissions';

function MyComponent() {
  const permissions = useThemePermissions();
  
  if (permissions.canManageAllThemes) {
    // عرض لوحة الأدمن
    return <AdminThemePanel />;
  } else if (permissions.canManageOwnTheme) {
    // عرض لوحة مدير المدرسة
    return <SchoolThemePanel />;
  } else {
    // عرض رسالة عدم وجود صلاحية
    return <NoPermissionMessage />;
  }
}
```

### 2. التنقل للصفحات:
```typescript
import { Link } from 'react-router-dom';
import { useThemePermissions } from '../hooks/useThemePermissions';

function NavigationMenu() {
  const permissions = useThemePermissions();
  
  return (
    <nav>
      {permissions.canManageAllThemes && (
        <Link to="/admin/themes">إدارة الثيمات</Link>
      )}
      
      {permissions.canManageOwnTheme && (
        <Link to="/school/theme">ثيم المدرسة</Link>
      )}
    </nav>
  );
}
```

---

## 🔄 التحديثات المستقبلية

### ميزات مخططة:
- [ ] معاينة الثيمات في الوقت الفعلي
- [ ] قوالب ثيمات جاهزة
- [ ] تصدير/استيراد الثيمات
- [ ] تاريخ التغييرات
- [ ] موافقة الأدمن على التغييرات

### تحسينات الأمان:
- [ ] تشفير إعدادات الثيمات
- [ ] سجل مفصل للتغييرات
- [ ] حد أقصى لحجم الملفات المرفوعة
- [ ] فحص الملفات للفيروسات

---

## 📞 الدعم

إذا واجهت أي مشاكل:
1. **تحقق من Console** للأخطاء
2. **فحص Network Tab** للطلبات الفاشلة
3. **راجع الصلاحيات** للمستخدم الحالي
4. **تأكد من تحديث الصفحة** بعد التغييرات

---

**تم إنشاء هذا الدليل في إطار المرحلة الثالثة من تطوير نظام إدارة الحافلات المدرسية** 🚌✨
