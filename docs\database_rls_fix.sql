-- إصلاح شامل لسياسات RLS
-- Comprehensive RLS Policy Fix

-- ===== 1. تنظيف السياسات الموجودة =====
-- Clean existing policies

-- إزالة جميع السياسات من جدول المستخدمين
DROP POLICY IF EXISTS "admin_full_access" ON users;
DROP POLICY IF EXISTS "school_manager_tenant_access" ON users;
DROP POLICY IF EXISTS "own_profile_access" ON users;
DROP POLICY IF EXISTS "tenant_users_view" ON users;
DROP POLICY IF EXISTS "users_own_profile" ON users;
DROP POLICY IF EXISTS "admin_access_all" ON users;
DROP POLICY IF EXISTS "school_manager_tenant" ON users;

-- إزالة جميع السياسات من جدول الطلاب
DROP POLICY IF EXISTS "admin_full_access" ON students;
DROP POLICY IF EXISTS "school_staff_tenant_access" ON students;
DROP POLICY IF EXISTS "parent_children_access" ON students;
DROP POLICY IF EXISTS "student_own_profile" ON students;
DROP POLICY IF EXISTS "driver_tenant_view" ON students;

-- إزالة جميع السياسات من جدول الحافلات
DROP POLICY IF EXISTS "admin_full_access" ON buses;
DROP POLICY IF EXISTS "school_staff_tenant_access" ON buses;
DROP POLICY IF EXISTS "driver_assigned_bus" ON buses;
DROP POLICY IF EXISTS "tenant_buses_view" ON buses;

-- إزالة جميع السياسات من جدول المسارات
DROP POLICY IF EXISTS "admin_full_access" ON routes;
DROP POLICY IF EXISTS "school_staff_tenant_access" ON routes;
DROP POLICY IF EXISTS "tenant_routes_view" ON routes;

-- إزالة جميع السياسات من جدول المدارس
DROP POLICY IF EXISTS "admin_full_access" ON tenants;
DROP POLICY IF EXISTS "school_manager_own_tenant" ON tenants;
DROP POLICY IF EXISTS "users_own_tenant_view" ON tenants;

-- إزالة جميع السياسات من جدول الحضور
DROP POLICY IF EXISTS "admin_full_access" ON attendance;
DROP POLICY IF EXISTS "tenant_staff_access" ON attendance;
DROP POLICY IF EXISTS "parent_children_attendance" ON attendance;

-- ===== 2. تعطيل RLS مؤقتاً =====
-- Temporarily disable RLS

ALTER TABLE users DISABLE ROW LEVEL SECURITY;
ALTER TABLE students DISABLE ROW LEVEL SECURITY;
ALTER TABLE buses DISABLE ROW LEVEL SECURITY;
ALTER TABLE routes DISABLE ROW LEVEL SECURITY;
ALTER TABLE tenants DISABLE ROW LEVEL SECURITY;
ALTER TABLE attendance DISABLE ROW LEVEL SECURITY;

-- ===== 3. إنشاء دوال مساعدة آمنة =====
-- Create safe helper functions

-- دالة للتحقق من كون المستخدم أدمن
CREATE OR REPLACE FUNCTION is_admin_user()
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- استخدام auth.jwt() لتجنب التكرار اللا نهائي
  RETURN COALESCE((auth.jwt() ->> 'role')::text = 'admin', false);
EXCEPTION
  WHEN OTHERS THEN
    RETURN false;
END;
$$;

-- دالة للحصول على tenant_id للمستخدم الحالي
CREATE OR REPLACE FUNCTION get_current_user_tenant_id()
RETURNS uuid
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- استخدام auth.jwt() لتجنب التكرار اللا نهائي
  RETURN COALESCE((auth.jwt() ->> 'tenant_id')::uuid, null);
EXCEPTION
  WHEN OTHERS THEN
    RETURN null;
END;
$$;

-- دالة للحصول على دور المستخدم الحالي
CREATE OR REPLACE FUNCTION get_current_user_role()
RETURNS text
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- استخدام auth.jwt() لتجنب التكرار اللا نهائي
  RETURN COALESCE((auth.jwt() ->> 'role')::text, 'student');
EXCEPTION
  WHEN OTHERS THEN
    RETURN 'student';
END;
$$;

-- منح الصلاحيات للدوال
GRANT EXECUTE ON FUNCTION is_admin_user() TO authenticated;
GRANT EXECUTE ON FUNCTION get_current_user_tenant_id() TO authenticated;
GRANT EXECUTE ON FUNCTION get_current_user_role() TO authenticated;

-- ===== 4. إنشاء سياسات بسيطة وآمنة =====
-- Create simple and safe policies

-- تفعيل RLS على جدول المستخدمين
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- سياسة 1: الأدمن يمكنه الوصول لجميع المستخدمين
CREATE POLICY "admin_users_access" ON users
  FOR ALL
  TO authenticated
  USING (is_admin_user())
  WITH CHECK (is_admin_user());

-- سياسة 2: المستخدمون يمكنهم الوصول لملفهم الشخصي
CREATE POLICY "users_own_profile" ON users
  FOR ALL
  TO authenticated
  USING (id = auth.uid())
  WITH CHECK (id = auth.uid());

-- سياسة 3: مديرو المدارس يمكنهم إدارة مستخدمي مدرستهم
CREATE POLICY "school_manager_users" ON users
  FOR ALL
  TO authenticated
  USING (
    get_current_user_role() = 'school_manager' AND
    tenant_id = get_current_user_tenant_id()
  )
  WITH CHECK (
    get_current_user_role() = 'school_manager' AND
    tenant_id = get_current_user_tenant_id()
  );

-- تفعيل RLS على جدول الطلاب
ALTER TABLE students ENABLE ROW LEVEL SECURITY;

-- سياسة 1: الأدمن يمكنه الوصول لجميع الطلاب
CREATE POLICY "admin_students_access" ON students
  FOR ALL
  TO authenticated
  USING (is_admin_user())
  WITH CHECK (is_admin_user());

-- سياسة 2: موظفو المدرسة يمكنهم إدارة طلاب مدرستهم
CREATE POLICY "school_staff_students" ON students
  FOR ALL
  TO authenticated
  USING (
    get_current_user_role() IN ('school_manager', 'supervisor') AND
    tenant_id = get_current_user_tenant_id()
  )
  WITH CHECK (
    get_current_user_role() IN ('school_manager', 'supervisor') AND
    tenant_id = get_current_user_tenant_id()
  );

-- سياسة 3: الطلاب يمكنهم عرض ملفهم الشخصي
CREATE POLICY "students_own_profile" ON students
  FOR SELECT
  TO authenticated
  USING (id = auth.uid());

-- سياسة 4: أولياء الأمور يمكنهم عرض أطفالهم
CREATE POLICY "parents_children" ON students
  FOR SELECT
  TO authenticated
  USING (
    get_current_user_role() = 'parent' AND
    parent_id = auth.uid()
  );

-- تفعيل RLS على جدول الحافلات
ALTER TABLE buses ENABLE ROW LEVEL SECURITY;

-- سياسة 1: الأدمن يمكنه الوصول لجميع الحافلات
CREATE POLICY "admin_buses_access" ON buses
  FOR ALL
  TO authenticated
  USING (is_admin_user())
  WITH CHECK (is_admin_user());

-- سياسة 2: موظفو المدرسة يمكنهم إدارة حافلات مدرستهم
CREATE POLICY "school_staff_buses" ON buses
  FOR ALL
  TO authenticated
  USING (
    get_current_user_role() IN ('school_manager', 'supervisor') AND
    tenant_id = get_current_user_tenant_id()
  )
  WITH CHECK (
    get_current_user_role() IN ('school_manager', 'supervisor') AND
    tenant_id = get_current_user_tenant_id()
  );

-- سياسة 3: السائقون يمكنهم عرض وتحديث حافلاتهم المخصصة
CREATE POLICY "drivers_assigned_buses" ON buses
  FOR ALL
  TO authenticated
  USING (
    get_current_user_role() = 'driver' AND
    (driver_id = auth.uid() OR tenant_id = get_current_user_tenant_id())
  )
  WITH CHECK (
    get_current_user_role() = 'driver' AND
    (driver_id = auth.uid() OR tenant_id = get_current_user_tenant_id())
  );

-- سياسة 4: جميع مستخدمي المدرسة يمكنهم عرض حافلات مدرستهم
CREATE POLICY "tenant_buses_view" ON buses
  FOR SELECT
  TO authenticated
  USING (tenant_id = get_current_user_tenant_id());

-- تفعيل RLS على جدول المسارات
ALTER TABLE routes ENABLE ROW LEVEL SECURITY;

-- سياسة 1: الأدمن يمكنه الوصول لجميع المسارات
CREATE POLICY "admin_routes_access" ON routes
  FOR ALL
  TO authenticated
  USING (is_admin_user())
  WITH CHECK (is_admin_user());

-- سياسة 2: موظفو المدرسة يمكنهم إدارة مسارات مدرستهم
CREATE POLICY "school_staff_routes" ON routes
  FOR ALL
  TO authenticated
  USING (
    get_current_user_role() IN ('school_manager', 'supervisor') AND
    tenant_id = get_current_user_tenant_id()
  )
  WITH CHECK (
    get_current_user_role() IN ('school_manager', 'supervisor') AND
    tenant_id = get_current_user_tenant_id()
  );

-- سياسة 3: جميع مستخدمي المدرسة يمكنهم عرض مسارات مدرستهم
CREATE POLICY "tenant_routes_view" ON routes
  FOR SELECT
  TO authenticated
  USING (tenant_id = get_current_user_tenant_id());

-- تفعيل RLS على جدول المدارس
ALTER TABLE tenants ENABLE ROW LEVEL SECURITY;

-- سياسة 1: الأدمن يمكنه الوصول لجميع المدارس
CREATE POLICY "admin_tenants_access" ON tenants
  FOR ALL
  TO authenticated
  USING (is_admin_user())
  WITH CHECK (is_admin_user());

-- سياسة 2: مديرو المدارس يمكنهم عرض وتحديث مدرستهم
CREATE POLICY "school_manager_own_tenant" ON tenants
  FOR ALL
  TO authenticated
  USING (
    get_current_user_role() = 'school_manager' AND
    id = get_current_user_tenant_id()
  )
  WITH CHECK (
    get_current_user_role() = 'school_manager' AND
    id = get_current_user_tenant_id()
  );

-- سياسة 3: جميع المستخدمين يمكنهم عرض مدرستهم
CREATE POLICY "users_own_tenant" ON tenants
  FOR SELECT
  TO authenticated
  USING (id = get_current_user_tenant_id());

-- تفعيل RLS على جدول الحضور
ALTER TABLE attendance ENABLE ROW LEVEL SECURITY;

-- سياسة 1: الأدمن يمكنه الوصول لجميع سجلات الحضور
CREATE POLICY "admin_attendance_access" ON attendance
  FOR ALL
  TO authenticated
  USING (is_admin_user())
  WITH CHECK (is_admin_user());

-- سياسة 2: موظفو المدرسة يمكنهم إدارة حضور مدرستهم
CREATE POLICY "school_staff_attendance" ON attendance
  FOR ALL
  TO authenticated
  USING (
    get_current_user_role() IN ('school_manager', 'supervisor', 'driver') AND
    tenant_id = get_current_user_tenant_id()
  )
  WITH CHECK (
    get_current_user_role() IN ('school_manager', 'supervisor', 'driver') AND
    tenant_id = get_current_user_tenant_id()
  );

-- سياسة 3: أولياء الأمور يمكنهم عرض حضور أطفالهم
CREATE POLICY "parents_children_attendance" ON attendance
  FOR SELECT
  TO authenticated
  USING (
    get_current_user_role() = 'parent' AND
    EXISTS (
      SELECT 1 FROM students s 
      WHERE s.id = attendance.student_id 
      AND s.parent_id = auth.uid()
    )
  );

-- ===== 5. إنشاء دوال RPC محسنة =====
-- Create improved RPC functions

-- دالة للحصول على جميع المستخدمين (للأدمن فقط)
CREATE OR REPLACE FUNCTION get_all_users()
RETURNS SETOF users
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- التحقق من كون المستخدم أدمن
  IF NOT is_admin_user() THEN
    RAISE EXCEPTION 'Access denied. Admin role required.';
  END IF;

  -- إرجاع جميع المستخدمين
  RETURN QUERY
  SELECT * FROM users
  ORDER BY created_at DESC;
END;
$$;

-- دالة للحصول على جميع الطلاب (للأدمن فقط)
CREATE OR REPLACE FUNCTION get_all_students()
RETURNS SETOF students
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- التحقق من كون المستخدم أدمن
  IF NOT is_admin_user() THEN
    RAISE EXCEPTION 'Access denied. Admin role required.';
  END IF;

  -- إرجاع جميع الطلاب
  RETURN QUERY
  SELECT * FROM students
  ORDER BY created_at DESC;
END;
$$;

-- دالة للحصول على جميع الحافلات (للأدمن فقط)
CREATE OR REPLACE FUNCTION get_all_buses()
RETURNS SETOF buses
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- التحقق من كون المستخدم أدمن
  IF NOT is_admin_user() THEN
    RAISE EXCEPTION 'Access denied. Admin role required.';
  END IF;

  -- إرجاع جميع الحافلات
  RETURN QUERY
  SELECT * FROM buses
  ORDER BY created_at DESC;
END;
$$;

-- دالة للحصول على جميع المسارات (للأدمن فقط)
CREATE OR REPLACE FUNCTION get_all_routes()
RETURNS SETOF routes
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- التحقق من كون المستخدم أدمن
  IF NOT is_admin_user() THEN
    RAISE EXCEPTION 'Access denied. Admin role required.';
  END IF;

  -- إرجاع جميع المسارات
  RETURN QUERY
  SELECT * FROM routes
  ORDER BY created_at DESC;
END;
$$;

-- دالة للحصول على جميع المدارس (للأدمن فقط)
CREATE OR REPLACE FUNCTION get_all_tenants()
RETURNS SETOF tenants
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- التحقق من كون المستخدم أدمن
  IF NOT is_admin_user() THEN
    RAISE EXCEPTION 'Access denied. Admin role required.';
  END IF;

  -- إرجاع جميع المدارس
  RETURN QUERY
  SELECT * FROM tenants
  ORDER BY created_at DESC;
END;
$$;

-- منح الصلاحيات للدوال
GRANT EXECUTE ON FUNCTION get_all_users() TO authenticated;
GRANT EXECUTE ON FUNCTION get_all_students() TO authenticated;
GRANT EXECUTE ON FUNCTION get_all_buses() TO authenticated;
GRANT EXECUTE ON FUNCTION get_all_routes() TO authenticated;
GRANT EXECUTE ON FUNCTION get_all_tenants() TO authenticated;

-- ===== 6. دوال إنشاء المستخدمين والطلاب =====
-- User and Student Creation Functions

-- دالة إنشاء طالب جديد
CREATE OR REPLACE FUNCTION create_student_user(
  user_email text,
  user_password text,
  user_name text,
  user_tenant_id uuid,
  student_grade text DEFAULT 'Grade 1',
  student_parent_id uuid DEFAULT NULL,
  student_route_stop_id uuid DEFAULT NULL
)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  new_user_id uuid;
  result json;
  current_role text;
  current_tenant_id uuid;
BEGIN
  -- التحقق من الصلاحيات
  current_role := get_current_user_role();
  current_tenant_id := get_current_user_tenant_id();

  -- التحقق من أن المستخدم الحالي يمكنه إنشاء طلاب
  IF NOT (is_admin_user() OR
          (current_role IN ('school_manager', 'supervisor') AND current_tenant_id = user_tenant_id)) THEN
    result := json_build_object(
      'success', false,
      'error', 'Access denied. Insufficient permissions to create students.',
      'message', 'You do not have permission to create students'
    );
    RETURN result;
  END IF;

  -- التحقق من صحة البيانات
  IF user_email IS NULL OR user_email = '' THEN
    result := json_build_object(
      'success', false,
      'error', 'Email is required',
      'message', 'Student email cannot be empty'
    );
    RETURN result;
  END IF;

  IF user_name IS NULL OR user_name = '' THEN
    result := json_build_object(
      'success', false,
      'error', 'Name is required',
      'message', 'Student name cannot be empty'
    );
    RETURN result;
  END IF;

  IF user_tenant_id IS NULL THEN
    result := json_build_object(
      'success', false,
      'error', 'Tenant ID is required',
      'message', 'Student must belong to a school'
    );
    RETURN result;
  END IF;

  -- التحقق من عدم وجود مستخدم بنفس البريد الإلكتروني
  IF EXISTS (SELECT 1 FROM users WHERE email = user_email) THEN
    result := json_build_object(
      'success', false,
      'error', 'Email already exists',
      'message', 'A user with this email already exists'
    );
    RETURN result;
  END IF;

  -- إنشاء معرف جديد للمستخدم
  new_user_id := gen_random_uuid();

  -- إدراج سجل المستخدم
  INSERT INTO users (
    id,
    email,
    name,
    role,
    tenant_id,
    is_active,
    created_at,
    updated_at
  ) VALUES (
    new_user_id,
    user_email,
    user_name,
    'student',
    user_tenant_id,
    true,
    now(),
    now()
  );

  -- إدراج سجل الطالب
  INSERT INTO students (
    id,
    name,
    grade,
    tenant_id,
    parent_id,
    route_stop_id,
    is_active,
    created_at,
    updated_at
  ) VALUES (
    new_user_id,
    user_name,
    student_grade,
    user_tenant_id,
    student_parent_id,
    student_route_stop_id,
    true,
    now(),
    now()
  );

  -- إرجاع نتيجة النجاح
  result := json_build_object(
    'success', true,
    'user_id', new_user_id,
    'message', 'Student created successfully',
    'data', json_build_object(
      'id', new_user_id,
      'email', user_email,
      'name', user_name,
      'role', 'student',
      'tenant_id', user_tenant_id
    )
  );

  RETURN result;

EXCEPTION
  WHEN OTHERS THEN
    -- إرجاع نتيجة الخطأ
    result := json_build_object(
      'success', false,
      'error', SQLERRM,
      'message', 'Failed to create student: ' || SQLERRM
    );
    RETURN result;
END;
$$;

-- دالة إنشاء مستخدم عادي
CREATE OR REPLACE FUNCTION create_regular_user(
  user_email text,
  user_password text,
  user_name text,
  user_role user_role,
  user_tenant_id uuid,
  user_phone text DEFAULT NULL
)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  new_user_id uuid;
  result json;
  current_role text;
  current_tenant_id uuid;
BEGIN
  -- التحقق من الصلاحيات
  current_role := get_current_user_role();
  current_tenant_id := get_current_user_tenant_id();

  -- التحقق من أن المستخدم الحالي يمكنه إنشاء مستخدمين
  IF NOT (is_admin_user() OR
          (current_role = 'school_manager' AND current_tenant_id = user_tenant_id)) THEN
    result := json_build_object(
      'success', false,
      'error', 'Access denied. Insufficient permissions to create users.',
      'message', 'You do not have permission to create users'
    );
    RETURN result;
  END IF;

  -- التحقق من صحة البيانات
  IF user_email IS NULL OR user_email = '' THEN
    result := json_build_object(
      'success', false,
      'error', 'Email is required',
      'message', 'User email cannot be empty'
    );
    RETURN result;
  END IF;

  IF user_name IS NULL OR user_name = '' THEN
    result := json_build_object(
      'success', false,
      'error', 'Name is required',
      'message', 'User name cannot be empty'
    );
    RETURN result;
  END IF;

  IF user_role IS NULL THEN
    result := json_build_object(
      'success', false,
      'error', 'Role is required',
      'message', 'User role must be specified'
    );
    RETURN result;
  END IF;

  -- التحقق من عدم وجود مستخدم بنفس البريد الإلكتروني
  IF EXISTS (SELECT 1 FROM users WHERE email = user_email) THEN
    result := json_build_object(
      'success', false,
      'error', 'Email already exists',
      'message', 'A user with this email already exists'
    );
    RETURN result;
  END IF;

  -- إنشاء معرف جديد للمستخدم
  new_user_id := gen_random_uuid();

  -- إدراج سجل المستخدم
  INSERT INTO users (
    id,
    email,
    name,
    role,
    tenant_id,
    phone,
    is_active,
    created_at,
    updated_at
  ) VALUES (
    new_user_id,
    user_email,
    user_name,
    user_role,
    user_tenant_id,
    user_phone,
    true,
    now(),
    now()
  );

  -- إرجاع نتيجة النجاح
  result := json_build_object(
    'success', true,
    'user_id', new_user_id,
    'message', 'User created successfully',
    'data', json_build_object(
      'id', new_user_id,
      'email', user_email,
      'name', user_name,
      'role', user_role,
      'tenant_id', user_tenant_id
    )
  );

  RETURN result;

EXCEPTION
  WHEN OTHERS THEN
    -- إرجاع نتيجة الخطأ
    result := json_build_object(
      'success', false,
      'error', SQLERRM,
      'message', 'Failed to create user: ' || SQLERRM
    );
    RETURN result;
END;
$$;

-- منح الصلاحيات للدوال الجديدة
GRANT EXECUTE ON FUNCTION create_student_user(text, text, text, uuid, text, uuid, uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION create_regular_user(text, text, text, user_role, uuid, text) TO authenticated;

-- ===== 7. إنشاء فهارس محسنة للأداء =====
-- Create optimized indexes for performance

-- فهارس للمستخدمين
CREATE INDEX IF NOT EXISTS idx_users_tenant_id ON users(tenant_id);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_active ON users(is_active);

-- فهارس للطلاب
CREATE INDEX IF NOT EXISTS idx_students_tenant_id ON students(tenant_id);
CREATE INDEX IF NOT EXISTS idx_students_parent_id ON students(parent_id);
CREATE INDEX IF NOT EXISTS idx_students_route_stop_id ON students(route_stop_id);
CREATE INDEX IF NOT EXISTS idx_students_active ON students(is_active);

-- فهارس للحافلات
CREATE INDEX IF NOT EXISTS idx_buses_tenant_id ON buses(tenant_id);
CREATE INDEX IF NOT EXISTS idx_buses_driver_id ON buses(driver_id);
CREATE INDEX IF NOT EXISTS idx_buses_active ON buses(is_active);

-- فهارس للمسارات
CREATE INDEX IF NOT EXISTS idx_routes_tenant_id ON routes(tenant_id);
CREATE INDEX IF NOT EXISTS idx_routes_bus_id ON routes(bus_id);
CREATE INDEX IF NOT EXISTS idx_routes_active ON routes(is_active);

-- فهارس للحضور
CREATE INDEX IF NOT EXISTS idx_attendance_tenant_id ON attendance(tenant_id);
CREATE INDEX IF NOT EXISTS idx_attendance_student_id ON attendance(student_id);
CREATE INDEX IF NOT EXISTS idx_attendance_date ON attendance(date);

-- ===== 8. تحديث إعدادات الأمان =====
-- Update security settings

-- تحديث إعدادات JWT
ALTER DATABASE postgres SET "app.jwt_secret" = 'your-jwt-secret-here';

-- تحديث إعدادات الجلسة
ALTER DATABASE postgres SET "app.session_timeout" = '3600'; -- ساعة واحدة

-- ===== انتهاء الإصلاح =====
-- End of fix

-- رسالة تأكيد
DO $$
BEGIN
  RAISE NOTICE 'تم تطبيق إصلاحات RLS بنجاح - RLS fixes applied successfully';
  RAISE NOTICE 'جميع السياسات تم إنشاؤها بشكل آمن - All policies created safely';
  RAISE NOTICE 'دوال إنشاء المستخدمين جاهزة - User creation functions ready';
END $$;
