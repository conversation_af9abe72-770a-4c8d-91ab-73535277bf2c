-- إصلاح هيكل قاعدة البيانات لنظام إدارة الحافلات المدرسية
-- Database Schema Fix for School Bus Management System

-- 1. إنشاء جدول الصيانة مع العمود المفقود
-- Create bus_maintenance table with missing scheduled_date column
CREATE TABLE IF NOT EXISTS bus_maintenance (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    bus_id UUID REFERENCES buses(id) ON DELETE CASCADE,
    maintenance_type VARCHAR(100) NOT NULL,
    description TEXT,
    status VARCHAR(50) DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'in_progress', 'completed', 'cancelled')),
    scheduled_date TIMESTAMP WITH TIME ZONE NOT NULL,
    completed_date TIMESTAMP WITH TIME ZONE,
    cost DECIMAL(10,2),
    notes TEXT,
    tenant_id UUID NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إضافة فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_bus_maintenance_bus_id ON bus_maintenance(bus_id);
CREATE INDEX IF NOT EXISTS idx_bus_maintenance_tenant_id ON bus_maintenance(tenant_id);
CREATE INDEX IF NOT EXISTS idx_bus_maintenance_scheduled_date ON bus_maintenance(scheduled_date);
CREATE INDEX IF NOT EXISTS idx_bus_maintenance_status ON bus_maintenance(status);

-- 2. إنشاء جدول مواقع الحافلات
-- Create bus_locations table for real-time tracking
CREATE TABLE IF NOT EXISTS bus_locations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    bus_id UUID REFERENCES buses(id) ON DELETE CASCADE,
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    speed DECIMAL(5,2) DEFAULT 0,
    heading DECIMAL(5,2) DEFAULT 0,
    accuracy DECIMAL(8,2) DEFAULT 0,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    tenant_id UUID NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إضافة فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_bus_locations_bus_id ON bus_locations(bus_id);
CREATE INDEX IF NOT EXISTS idx_bus_locations_tenant_id ON bus_locations(tenant_id);
CREATE INDEX IF NOT EXISTS idx_bus_locations_timestamp ON bus_locations(timestamp);

-- 3. إنشاء جدول المسارات مع محطات التوقف
-- Create routes table with stops as JSONB
CREATE TABLE IF NOT EXISTS routes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    bus_id UUID REFERENCES buses(id) ON DELETE SET NULL,
    stops JSONB DEFAULT '[]'::jsonb,
    is_active BOOLEAN DEFAULT true,
    tenant_id UUID NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إضافة فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_routes_bus_id ON routes(bus_id);
CREATE INDEX IF NOT EXISTS idx_routes_tenant_id ON routes(tenant_id);
CREATE INDEX IF NOT EXISTS idx_routes_is_active ON routes(is_active);

-- 4. تحديث جدول الحافلات إذا لزم الأمر
-- Update buses table if needed
ALTER TABLE buses 
ADD COLUMN IF NOT EXISTS last_location JSONB,
ADD COLUMN IF NOT EXISTS last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
ADD COLUMN IF NOT EXISTS current_students INTEGER DEFAULT 0;

-- 5. إنشاء جدول محطات التوقف منفصل (اختياري)
-- Create separate bus_stops table (optional)
CREATE TABLE IF NOT EXISTS bus_stops (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    address TEXT,
    tenant_id UUID NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إضافة فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_bus_stops_tenant_id ON bus_stops(tenant_id);

-- 6. إنشاء جدول ربط المسارات بمحطات التوقف
-- Create route_stops junction table
CREATE TABLE IF NOT EXISTS route_stops (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    route_id UUID REFERENCES routes(id) ON DELETE CASCADE,
    stop_id UUID REFERENCES bus_stops(id) ON DELETE CASCADE,
    stop_order INTEGER NOT NULL,
    estimated_arrival_time TIME,
    tenant_id UUID NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إضافة فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_route_stops_route_id ON route_stops(route_id);
CREATE INDEX IF NOT EXISTS idx_route_stops_stop_id ON route_stops(stop_id);
CREATE INDEX IF NOT EXISTS idx_route_stops_tenant_id ON route_stops(tenant_id);

-- 7. إنشاء دوال مساعدة
-- Create helper functions

-- دالة للحصول على آخر موقع للحافلة
CREATE OR REPLACE FUNCTION get_latest_bus_location(bus_uuid UUID)
RETURNS TABLE(latitude DECIMAL, longitude DECIMAL, speed DECIMAL, heading DECIMAL, timestamp TIMESTAMP WITH TIME ZONE)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT bl.latitude, bl.longitude, bl.speed, bl.heading, bl.timestamp
    FROM bus_locations bl
    WHERE bl.bus_id = bus_uuid
    ORDER BY bl.timestamp DESC
    LIMIT 1;
END;
$$;

-- دالة للحصول على تنبيهات الصيانة المستحقة
CREATE OR REPLACE FUNCTION get_maintenance_alerts(tenant_uuid UUID)
RETURNS TABLE(
    id UUID,
    bus_id UUID,
    bus_plate_number VARCHAR,
    maintenance_type VARCHAR,
    scheduled_date TIMESTAMP WITH TIME ZONE,
    days_overdue INTEGER
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        bm.id,
        bm.bus_id,
        b.plate_number,
        bm.maintenance_type,
        bm.scheduled_date,
        EXTRACT(DAY FROM (NOW() - bm.scheduled_date))::INTEGER as days_overdue
    FROM bus_maintenance bm
    JOIN buses b ON bm.bus_id = b.id
    WHERE bm.tenant_id = tenant_uuid
    AND bm.status = 'scheduled'
    AND bm.scheduled_date <= NOW()
    ORDER BY bm.scheduled_date ASC;
END;
$$;

-- 8. إنشاء triggers للتحديث التلقائي
-- Create triggers for automatic updates

-- Trigger لتحديث updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- تطبيق trigger على الجداول المطلوبة
CREATE TRIGGER update_bus_maintenance_updated_at BEFORE UPDATE ON bus_maintenance FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_routes_updated_at BEFORE UPDATE ON routes FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_bus_stops_updated_at BEFORE UPDATE ON bus_stops FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 9. إدراج بيانات تجريبية (اختياري)
-- Insert sample data (optional)

-- إدراج محطات توقف تجريبية
INSERT INTO bus_stops (name, latitude, longitude, address, tenant_id) VALUES
('محطة المدرسة الرئيسية', 24.7136, 46.6753, 'الرياض، المملكة العربية السعودية', '00000000-0000-0000-0000-000000000001'),
('حي النرجس', 24.7200, 46.6800, 'حي النرجس، الرياض', '00000000-0000-0000-0000-000000000001'),
('حي الملقا', 24.7300, 46.6900, 'حي الملقا، الرياض', '00000000-0000-0000-0000-000000000001')
ON CONFLICT DO NOTHING;

-- 10. إعداد Row Level Security (RLS)
-- Setup Row Level Security

-- تفعيل RLS على الجداول
ALTER TABLE bus_maintenance ENABLE ROW LEVEL SECURITY;
ALTER TABLE bus_locations ENABLE ROW LEVEL SECURITY;
ALTER TABLE routes ENABLE ROW LEVEL SECURITY;
ALTER TABLE bus_stops ENABLE ROW LEVEL SECURITY;
ALTER TABLE route_stops ENABLE ROW LEVEL SECURITY;

-- إنشاء سياسات RLS
CREATE POLICY "Users can view maintenance for their tenant" ON bus_maintenance
    FOR SELECT USING (tenant_id = auth.jwt() ->> 'tenant_id'::text);

CREATE POLICY "Users can view locations for their tenant" ON bus_locations
    FOR SELECT USING (tenant_id = auth.jwt() ->> 'tenant_id'::text);

CREATE POLICY "Users can view routes for their tenant" ON routes
    FOR SELECT USING (tenant_id = auth.jwt() ->> 'tenant_id'::text);

CREATE POLICY "Users can view stops for their tenant" ON bus_stops
    FOR SELECT USING (tenant_id = auth.jwt() ->> 'tenant_id'::text);

-- 11. إنشاء views مفيدة
-- Create useful views

-- عرض للحافلات مع آخر موقع
CREATE OR REPLACE VIEW buses_with_location AS
SELECT 
    b.*,
    bl.latitude,
    bl.longitude,
    bl.speed,
    bl.heading,
    bl.timestamp as location_timestamp
FROM buses b
LEFT JOIN LATERAL (
    SELECT latitude, longitude, speed, heading, timestamp
    FROM bus_locations
    WHERE bus_id = b.id
    ORDER BY timestamp DESC
    LIMIT 1
) bl ON true;

-- عرض للصيانة مع تفاصيل الحافلة
CREATE OR REPLACE VIEW maintenance_with_bus AS
SELECT 
    bm.*,
    b.plate_number,
    b.model,
    b.driver_name
FROM bus_maintenance bm
JOIN buses b ON bm.bus_id = b.id;

-- 12. منح الصلاحيات
-- Grant permissions

-- منح صلاحيات للمستخدمين المصرح لهم
GRANT SELECT, INSERT, UPDATE, DELETE ON bus_maintenance TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON bus_locations TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON routes TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON bus_stops TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON route_stops TO authenticated;

-- منح صلاحيات على الـ views
GRANT SELECT ON buses_with_location TO authenticated;
GRANT SELECT ON maintenance_with_bus TO authenticated;

-- منح صلاحيات على الدوال
GRANT EXECUTE ON FUNCTION get_latest_bus_location(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_maintenance_alerts(UUID) TO authenticated;
