-- Create create_regular_user RPC function that calls the Edge Function
-- This function acts as a bridge between the client and the Edge Function

CREATE OR REPLACE FUNCTION create_regular_user(
  user_email text,
  user_password text,
  user_name text,
  user_role text,
  user_tenant_id uuid
)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
DECLARE
  result json;
  function_url text;
  response_data text;
BEGIN
  -- This function should call the Edge Function
  -- For now, we'll return an error directing to use the proper approach
  
  result := json_build_object(
    'success', false,
    'error', 'This function should call the create-user Edge Function',
    'message', 'Please use the Edge Function directly for user creation'
  );
  
  RETURN result;
  
EXCEPTION
  WHEN OTHERS THEN
    result := json_build_object(
      'success', false,
      'error', SQLERRM,
      'message', 'Failed to create user'
    );
    RETURN result;
END;
$$;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION create_regular_user(text, text, text, text, uuid) TO authenticated;
