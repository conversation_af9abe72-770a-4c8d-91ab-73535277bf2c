-- إصلاح مشاكل Foreign Key Constraints
-- Fix Foreign Key Constraint Issues

-- ===== فحص وإصلاح القيود الخارجية =====
-- Check and fix foreign key constraints

-- 1. فحص القيود الموجودة على جدول users
SELECT 
    tc.constraint_name, 
    tc.table_name, 
    kcu.column_name, 
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name 
FROM 
    information_schema.table_constraints AS tc 
    JOIN information_schema.key_column_usage AS kcu
      ON tc.constraint_name = kcu.constraint_name
      AND tc.table_schema = kcu.table_schema
    JOIN information_schema.constraint_column_usage AS ccu
      ON ccu.constraint_name = tc.constraint_name
      AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY' 
  AND tc.table_name='users';

-- 2. إزالة القيد الخارجي المشكل إذا كان موجوداً
DROP CONSTRAINT IF EXISTS users_id_fkey ON users;

-- 3. التأكد من أن جدول auth.users موجود ويحتوي على البيانات المطلوبة
-- (هذا جدول Supabase الأساسي للمصادقة)

-- 4. إعادة إنشاء القيد الخارجي بشكل صحيح إذا لزم الأمر
-- (عادة لا نحتاج قيد خارجي على users.id لأنه المفتاح الأساسي)

-- 5. التحقق من أن جدول tenants يحتوي على بيانات
SELECT COUNT(*) as tenants_count FROM tenants WHERE is_active = true;

-- 6. إنشاء tenant افتراضي إذا لم يكن موجوداً
INSERT INTO tenants (id, name, is_active, created_at, updated_at)
SELECT 
  gen_random_uuid(),
  'Default School',
  true,
  now(),
  now()
WHERE NOT EXISTS (SELECT 1 FROM tenants WHERE is_active = true);

-- 7. دالة محسنة لإنشاء المستخدمين مع معالجة أفضل للأخطاء
CREATE OR REPLACE FUNCTION create_user_account_fixed(
  user_email text,
  user_password text,
  user_name text,
  user_role user_role,
  user_tenant_id uuid DEFAULT NULL,
  user_phone text DEFAULT NULL
)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  new_user_id uuid;
  result json;
  current_user_role text;
  current_tenant_id uuid;
  target_tenant_id uuid;
BEGIN
  -- التحقق من الصلاحيات
  SELECT role::text, tenant_id INTO current_user_role, current_tenant_id 
  FROM users WHERE id = auth.uid();
  
  -- تحديد tenant_id المستهدف
  target_tenant_id := user_tenant_id;
  
  -- للأدمن: إذا لم يتم تحديد tenant_id، استخدم أول tenant متاح
  IF current_user_role = 'admin' AND target_tenant_id IS NULL THEN
    SELECT id INTO target_tenant_id FROM tenants WHERE is_active = true LIMIT 1;
    
    IF target_tenant_id IS NULL THEN
      RETURN json_build_object(
        'success', false,
        'error', 'NO_TENANT_AVAILABLE',
        'message', 'No active school found. Please create a school first.'
      );
    END IF;
  END IF;
  
  -- لمدير المدرسة: استخدم tenant_id الخاص به
  IF current_user_role = 'school_manager' AND target_tenant_id IS NULL THEN
    target_tenant_id := current_tenant_id;
  END IF;
  
  -- التحقق من الصلاحيات
  IF NOT (
    current_user_role = 'admin' OR 
    (current_user_role = 'school_manager' AND current_tenant_id = target_tenant_id AND user_role != 'admin')
  ) THEN
    RETURN json_build_object(
      'success', false,
      'error', 'PERMISSION_DENIED',
      'message', 'You do not have permission to create users'
    );
  END IF;

  -- التحقق من صحة البيانات
  IF user_email IS NULL OR user_email = '' THEN
    RETURN json_build_object(
      'success', false,
      'error', 'VALIDATION_ERROR',
      'message', 'Email is required'
    );
  END IF;

  IF user_name IS NULL OR user_name = '' THEN
    RETURN json_build_object(
      'success', false,
      'error', 'VALIDATION_ERROR',
      'message', 'Name is required'
    );
  END IF;

  IF target_tenant_id IS NULL THEN
    RETURN json_build_object(
      'success', false,
      'error', 'VALIDATION_ERROR',
      'message', 'School assignment is required'
    );
  END IF;

  -- التحقق من وجود المدرسة
  IF NOT EXISTS (SELECT 1 FROM tenants WHERE id = target_tenant_id AND is_active = true) THEN
    RETURN json_build_object(
      'success', false,
      'error', 'INVALID_TENANT',
      'message', 'Selected school is not valid or inactive'
    );
  END IF;

  -- التحقق من عدم وجود مستخدم بنفس البريد الإلكتروني
  IF EXISTS (SELECT 1 FROM users WHERE email = user_email) THEN
    RETURN json_build_object(
      'success', false,
      'error', 'EMAIL_EXISTS',
      'message', 'A user with this email already exists'
    );
  END IF;

  -- إنشاء معرف جديد للمستخدم
  new_user_id := gen_random_uuid();
  
  -- إدراج سجل المستخدم بدون قيود خارجية مشكلة
  INSERT INTO users (
    id,
    email,
    name,
    role,
    tenant_id,
    phone,
    is_active,
    created_at,
    updated_at
  ) VALUES (
    new_user_id,
    user_email,
    user_name,
    user_role,
    target_tenant_id,
    user_phone,
    true,
    now(),
    now()
  );
  
  -- إرجاع نتيجة النجاح
  RETURN json_build_object(
    'success', true,
    'user_id', new_user_id,
    'message', 'User created successfully',
    'data', json_build_object(
      'id', new_user_id,
      'email', user_email,
      'name', user_name,
      'role', user_role,
      'tenant_id', target_tenant_id
    )
  );
  
EXCEPTION
  WHEN foreign_key_violation THEN
    RETURN json_build_object(
      'success', false,
      'error', 'FOREIGN_KEY_ERROR',
      'message', 'Invalid reference to related data. Please check school assignment.'
    );
  WHEN unique_violation THEN
    RETURN json_build_object(
      'success', false,
      'error', 'DUPLICATE_EMAIL',
      'message', 'A user with this email already exists'
    );
  WHEN OTHERS THEN
    RETURN json_build_object(
      'success', false,
      'error', 'DATABASE_ERROR',
      'message', 'Failed to create user: ' || SQLERRM
    );
END;
$$;

-- 8. دالة محسنة لإنشاء الطلاب
CREATE OR REPLACE FUNCTION create_student_account_fixed(
  student_email text,
  student_password text,
  student_name text,
  student_grade text,
  student_tenant_id uuid DEFAULT NULL,
  student_parent_id uuid DEFAULT NULL,
  student_route_stop_id uuid DEFAULT NULL
)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  new_user_id uuid;
  result json;
  current_user_role text;
  current_tenant_id uuid;
  target_tenant_id uuid;
BEGIN
  -- التحقق من الصلاحيات
  SELECT role::text, tenant_id INTO current_user_role, current_tenant_id 
  FROM users WHERE id = auth.uid();
  
  -- تحديد tenant_id المستهدف
  target_tenant_id := student_tenant_id;
  
  -- للأدمن: إذا لم يتم تحديد tenant_id، استخدم أول tenant متاح
  IF current_user_role = 'admin' AND target_tenant_id IS NULL THEN
    SELECT id INTO target_tenant_id FROM tenants WHERE is_active = true LIMIT 1;
  END IF;
  
  -- لمدير المدرسة: استخدم tenant_id الخاص به
  IF current_user_role IN ('school_manager', 'supervisor') AND target_tenant_id IS NULL THEN
    target_tenant_id := current_tenant_id;
  END IF;
  
  IF NOT (
    current_user_role = 'admin' OR 
    (current_user_role IN ('school_manager', 'supervisor') AND current_tenant_id = target_tenant_id)
  ) THEN
    RETURN json_build_object(
      'success', false,
      'error', 'PERMISSION_DENIED',
      'message', 'You do not have permission to create students'
    );
  END IF;

  -- التحقق من صحة البيانات
  IF student_email IS NULL OR student_email = '' THEN
    RETURN json_build_object(
      'success', false,
      'error', 'VALIDATION_ERROR',
      'message', 'Email is required'
    );
  END IF;

  IF student_name IS NULL OR student_name = '' THEN
    RETURN json_build_object(
      'success', false,
      'error', 'VALIDATION_ERROR',
      'message', 'Name is required'
    );
  END IF;

  IF target_tenant_id IS NULL THEN
    RETURN json_build_object(
      'success', false,
      'error', 'VALIDATION_ERROR',
      'message', 'School is required'
    );
  END IF;

  -- التحقق من عدم وجود مستخدم بنفس البريد الإلكتروني
  IF EXISTS (SELECT 1 FROM users WHERE email = student_email) THEN
    RETURN json_build_object(
      'success', false,
      'error', 'EMAIL_EXISTS',
      'message', 'A user with this email already exists'
    );
  END IF;

  -- إنشاء معرف جديد للمستخدم
  new_user_id := gen_random_uuid();
  
  -- إدراج سجل المستخدم
  INSERT INTO users (
    id,
    email,
    name,
    role,
    tenant_id,
    is_active,
    created_at,
    updated_at
  ) VALUES (
    new_user_id,
    student_email,
    student_name,
    'student',
    target_tenant_id,
    true,
    now(),
    now()
  );
  
  -- إدراج سجل الطالب
  INSERT INTO students (
    id,
    name,
    grade,
    tenant_id,
    parent_id,
    route_stop_id,
    is_active,
    created_at,
    updated_at
  ) VALUES (
    new_user_id,
    student_name,
    student_grade,
    target_tenant_id,
    student_parent_id,
    student_route_stop_id,
    true,
    now(),
    now()
  );
  
  RETURN json_build_object(
    'success', true,
    'user_id', new_user_id,
    'message', 'Student created successfully',
    'data', json_build_object(
      'id', new_user_id,
      'email', student_email,
      'name', student_name,
      'grade', student_grade,
      'tenant_id', target_tenant_id
    )
  );
  
EXCEPTION
  WHEN foreign_key_violation THEN
    RETURN json_build_object(
      'success', false,
      'error', 'FOREIGN_KEY_ERROR',
      'message', 'Invalid reference to related data. Please check school assignment.'
    );
  WHEN unique_violation THEN
    RETURN json_build_object(
      'success', false,
      'error', 'DUPLICATE_EMAIL',
      'message', 'A user with this email already exists'
    );
  WHEN OTHERS THEN
    RETURN json_build_object(
      'success', false,
      'error', 'DATABASE_ERROR',
      'message', 'Failed to create student: ' || SQLERRM
    );
END;
$$;

-- منح الصلاحيات للدوال الجديدة
GRANT EXECUTE ON FUNCTION create_user_account_fixed(text, text, text, user_role, uuid, text) TO authenticated;
GRANT EXECUTE ON FUNCTION create_student_account_fixed(text, text, text, text, uuid, uuid, uuid) TO authenticated;

-- رسالة تأكيد
SELECT 'Foreign key constraints fixed and enhanced functions created' as status;
