# 🧪 دليل الاختبار الشامل - Comprehensive Testing Guide

## 📋 **نظرة عامة - Overview**

هذا الدليل يوضح كيفية اختبار جميع الإصلاحات المطبقة على نظام إدارة الحافلات المدرسية.

This guide explains how to test all the fixes applied to the school bus management system.

---

## 🚀 **الخطوة الأولى: تطبيق الإصلاحات**

### **1. تشغيل سكريبت الإصلاحات**

```bash
# تأكد من وجود متغيرات البيئة
# Make sure environment variables are set
echo $VITE_SUPABASE_URL
echo $VITE_SUPABASE_ANON_KEY

# تشغيل سكريبت الإصلاحات
# Run the fixes script
node apply_database_fixes.js
```

### **2. إعادة تشغيل التطبيق**

```bash
# إيقاف الخادم
# Stop the server
Ctrl + C

# مسح cache المتصفح
# Clear browser cache
Ctrl + Shift + R

# إعادة تشغيل
# Restart
npm run dev
```

---

## 🔍 **الخطوة الثانية: اختبار مشاكل الأدمن**

### **1. اختبار صفحات الأدمن المتأثرة**

#### **أ. صفحة التتبع (Tracking)**
```
URL: http://localhost:[PORT]/dashboard/tracking
```

**النتائج المتوقعة:**
- ✅ الصفحة تحمل بدون infinite loading
- ✅ تظهر بيانات الحافلات والمسارات
- ✅ الخريطة تعمل بشكل طبيعي
- ✅ لا توجد أخطاء في Console

**رسائل Console المتوقعة:**
```
🔍 TrackingPage render: { loading: false, error: null, busesCount: X, user: true }
✅ TrackingPage rendering successfully with data
```

#### **ب. صفحة الصيانة (Maintenance)**
```
URL: http://localhost:[PORT]/dashboard/maintenance
```

**النتائج المتوقعة:**
- ✅ الصفحة تحمل بسرعة
- ✅ تظهر إحصائيات الصيانة
- ✅ تظهر التنبيهات والجدولة
- ✅ جميع التبويبات تعمل

**رسائل Console المتوقعة:**
```
🔄 MaintenancePage: Loading maintenance data for user: admin
🔧 MaintenancePage: Loading admin maintenance data
✅ MaintenancePage: Admin data loaded successfully
✅ MaintenancePage: Loading completed
```

#### **ج. صفحة الحضور المتقدم (Advanced Attendance)**
```
URL: http://localhost:[PORT]/dashboard/advanced-attendance
```

**النتائج المتوقعة:**
- ✅ الصفحة تحمل بدون مشاكل
- ✅ تظهر إحصائيات الحضور العامة
- ✅ تظهر الجلسات النشطة
- ✅ جميع الوظائف تعمل

**رسائل Console المتوقعة:**
```
🔄 AdvancedAttendancePage: Loading attendance data for user: admin
🔧 AdvancedAttendancePage: Loading admin attendance data
✅ AdvancedAttendancePage: Admin data loaded successfully
✅ AdvancedAttendancePage: Loading completed
```

---

## 👥 **الخطوة الثالثة: اختبار إنشاء المستخدمين**

### **1. اختبار إنشاء طالب من مدير المدرسة**

#### **الخطوات:**
1. تسجيل الدخول كمدير مدرسة
2. الانتقال إلى: `http://localhost:[PORT]/dashboard/students`
3. النقر على "Add Student"
4. ملء البيانات:
   - الاسم: "أحمد محمد"
   - البريد الإلكتروني: "<EMAIL>"
   - كلمة المرور: "Test123456"
   - الصف: "Grade 5"
5. النقر على "Save"

#### **النتائج المتوقعة:**
- ✅ لا توجد أخطاء في Console
- ✅ رسالة نجاح تظهر
- ✅ الطالب يظهر في قائمة الطلاب
- ✅ تم إنشاء حساب مستخدم للطالب

#### **رسائل Console المتوقعة:**
```
Creating student user with data: { email: "<EMAIL>", name: "أحمد محمد", ... }
Student creation response: { functionResult: { success: true, ... }, functionError: null }
Student created successfully: { success: true, user_id: "...", ... }
```

### **2. اختبار إنشاء مستخدم عادي**

#### **الخطوات:**
1. الانتقال إلى: `http://localhost:[PORT]/dashboard/users`
2. النقر على "Add User"
3. ملء البيانات:
   - الاسم: "سالم أحمد"
   - البريد الإلكتروني: "<EMAIL>"
   - كلمة المرور: "Test123456"
   - الدور: "supervisor"
4. النقر على "Save"

#### **النتائج المتوقعة:**
- ✅ لا توجد أخطاء في Console
- ✅ رسالة نجاح تظهر
- ✅ المستخدم يظهر في قائمة المستخدمين

---

## 🔧 **الخطوة الرابعة: اختبار DatabaseContext**

### **1. فحص تحميل البيانات للأدمن**

#### **رسائل Console المتوقعة:**
```
🔄 DatabaseContext: Starting data fetch for user: { id: "...", role: "admin", ... }
🔧 DatabaseContext: Fetching all data for admin user
🔄 DatabaseContext: Fetching admin data with direct queries...
✅ DatabaseContext: Fetched X users
✅ DatabaseContext: Fetched X buses
✅ DatabaseContext: Fetched X routes
✅ DatabaseContext: Fetched X students
✅ DatabaseContext: Fetched X tenants
🎉 DatabaseContext: Admin data fetch completed successfully
✅ DatabaseContext: Setting loading to false
```

### **2. فحص عدم وجود أخطاء RLS**

#### **الأخطاء التي يجب ألا تظهر:**
- ❌ `infinite recursion detected in policy`
- ❌ `42P17 error code`
- ❌ `operator does not exist: students ? unknown`
- ❌ `Edge Function returned a non-2xx status code`

---

## 📊 **الخطوة الخامسة: اختبار الوظائف العامة**

### **1. اختبار التنقل بين الصفحات**
- ✅ جميع صفحات الأدمن تحمل بسرعة
- ✅ لا توجد infinite loading
- ✅ البيانات تظهر بشكل صحيح

### **2. اختبار الأدوار المختلفة**
- ✅ الأدمن يرى جميع البيانات
- ✅ مدير المدرسة يرى بيانات مدرسته فقط
- ✅ المشرف يرى البيانات المناسبة
- ✅ السائق يرى حافلته المخصصة

### **3. اختبار الأمان**
- ✅ المستخدمون لا يمكنهم الوصول لبيانات مدارس أخرى
- ✅ الصلاحيات تعمل بشكل صحيح
- ✅ لا توجد تسريبات في البيانات

---

## 🚨 **استكشاف الأخطاء**

### **إذا استمرت مشاكل Loading:**

1. **فحص Console للأخطاء:**
```javascript
// افتح Console وابحث عن:
F12 → Console
// ابحث عن رسائل تحتوي على:
- "Error"
- "Failed"
- "❌"
```

2. **فحص Network Tab:**
```javascript
F12 → Network
// ابحث عن:
- طلبات فاشلة (حمراء)
- طلبات بطيئة (أكثر من 5 ثوان)
- أخطاء 400/500
```

3. **إعادة تطبيق الإصلاحات:**
```bash
node apply_database_fixes.js
npm run dev
```

### **إذا فشل إنشاء المستخدمين:**

1. **فحص رسائل الخطأ في Console**
2. **التأكد من وجود tenant_id**
3. **فحص صلاحيات المستخدم الحالي**

---

## ✅ **معايير النجاح**

### **الإصلاح ناجح إذا:**
- ✅ جميع صفحات الأدمن تحمل بسرعة (أقل من 3 ثوان)
- ✅ يمكن إنشاء طلاب ومستخدمين بدون أخطاء
- ✅ لا توجد أخطاء RLS في Console
- ✅ جميع البيانات تظهر بشكل صحيح
- ✅ التنقل بين الصفحات سلس
- ✅ الصلاحيات تعمل بشكل صحيح

### **الإصلاح يحتاج مراجعة إذا:**
- ❌ أي صفحة تظهر infinite loading
- ❌ أخطاء في إنشاء المستخدمين
- ❌ أخطاء RLS في Console
- ❌ بيانات لا تظهر أو تظهر بشكل خاطئ
- ❌ مشاكل في الصلاحيات

---

## 📞 **التواصل للدعم**

إذا واجهت أي مشاكل، يرجى إرسال:

1. **لقطة شاشة من Console** (F12 → Console)
2. **رسائل الخطأ كاملة**
3. **الخطوات التي أدت للمشكلة**
4. **دور المستخدم المستخدم في الاختبار**
5. **URL الصفحة المتأثرة**

**🎯 الهدف: نظام يعمل بكفاءة 100% بدون أخطاء!**
