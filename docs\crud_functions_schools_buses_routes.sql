-- دوال CRUD للمدارس والحافلات والمسارات
-- CRUD Functions for Schools, Buses, and Routes

-- ===== دوال إدارة المدارس =====
-- School/Tenant Management Functions

-- دالة إنشاء مدرسة جديدة (للأدمن فقط)
CREATE OR REPLACE FUNCTION create_school_tenant(
  school_name text,
  school_address text DEFAULT NULL,
  school_phone text DEFAULT NULL,
  school_email text DEFAULT NULL,
  school_settings jsonb DEFAULT '{}'::jsonb
)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  new_tenant_id uuid;
  result json;
  current_user_role text;
BEGIN
  -- التحقق من أن المستخدم أدمن
  SELECT role::text INTO current_user_role FROM users WHERE id = auth.uid();
  
  IF current_user_role != 'admin' THEN
    RETURN json_build_object(
      'success', false,
      'error', 'PERMISSION_DENIED',
      'message', 'Only admins can create schools'
    );
  END IF;

  -- التحقق من صحة البيانات
  IF school_name IS NULL OR school_name = '' THEN
    RETURN json_build_object(
      'success', false,
      'error', 'VALIDATION_ERROR',
      'message', 'School name is required'
    );
  END IF;

  -- إنشاء معرف جديد للمدرسة
  new_tenant_id := gen_random_uuid();
  
  -- إدراج سجل المدرسة
  INSERT INTO tenants (
    id,
    name,
    address,
    phone,
    email,
    settings,
    is_active,
    created_at,
    updated_at
  ) VALUES (
    new_tenant_id,
    school_name,
    school_address,
    school_phone,
    school_email,
    school_settings,
    true,
    now(),
    now()
  );
  
  RETURN json_build_object(
    'success', true,
    'tenant_id', new_tenant_id,
    'message', 'School created successfully',
    'data', json_build_object(
      'id', new_tenant_id,
      'name', school_name,
      'address', school_address,
      'phone', school_phone,
      'email', school_email
    )
  );
  
EXCEPTION
  WHEN OTHERS THEN
    RETURN json_build_object(
      'success', false,
      'error', 'DATABASE_ERROR',
      'message', 'Failed to create school: ' || SQLERRM
    );
END;
$$;

-- دالة تحديث مدرسة
CREATE OR REPLACE FUNCTION update_school_tenant(
  target_tenant_id uuid,
  school_name text DEFAULT NULL,
  school_address text DEFAULT NULL,
  school_phone text DEFAULT NULL,
  school_email text DEFAULT NULL,
  school_settings jsonb DEFAULT NULL
)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result json;
  current_user_role text;
  current_tenant_id uuid;
BEGIN
  -- الحصول على معلومات المستخدم الحالي
  SELECT role::text, tenant_id INTO current_user_role, current_tenant_id 
  FROM users WHERE id = auth.uid();
  
  -- التحقق من الصلاحيات
  IF NOT (
    current_user_role = 'admin' OR 
    (current_user_role = 'school_manager' AND current_tenant_id = target_tenant_id)
  ) THEN
    RETURN json_build_object(
      'success', false,
      'error', 'PERMISSION_DENIED',
      'message', 'You do not have permission to update this school'
    );
  END IF;

  -- تحديث البيانات
  UPDATE tenants SET
    name = COALESCE(school_name, name),
    address = COALESCE(school_address, address),
    phone = COALESCE(school_phone, phone),
    email = COALESCE(school_email, email),
    settings = COALESCE(school_settings, settings),
    updated_at = now()
  WHERE id = target_tenant_id;
  
  IF NOT FOUND THEN
    RETURN json_build_object(
      'success', false,
      'error', 'SCHOOL_NOT_FOUND',
      'message', 'School not found'
    );
  END IF;
  
  RETURN json_build_object(
    'success', true,
    'message', 'School updated successfully'
  );
  
EXCEPTION
  WHEN OTHERS THEN
    RETURN json_build_object(
      'success', false,
      'error', 'DATABASE_ERROR',
      'message', 'Failed to update school: ' || SQLERRM
    );
END;
$$;

-- ===== دوال إدارة الحافلات =====
-- Bus Management Functions

-- دالة إنشاء حافلة جديدة
CREATE OR REPLACE FUNCTION create_bus(
  bus_plate_number text,
  bus_capacity integer,
  bus_tenant_id uuid,
  bus_driver_id uuid DEFAULT NULL,
  bus_model text DEFAULT NULL,
  bus_year integer DEFAULT NULL
)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  new_bus_id uuid;
  result json;
  current_user_role text;
  current_tenant_id uuid;
BEGIN
  -- التحقق من الصلاحيات
  SELECT role::text, tenant_id INTO current_user_role, current_tenant_id 
  FROM users WHERE id = auth.uid();
  
  IF NOT (
    current_user_role = 'admin' OR 
    (current_user_role IN ('school_manager', 'supervisor') AND current_tenant_id = bus_tenant_id)
  ) THEN
    RETURN json_build_object(
      'success', false,
      'error', 'PERMISSION_DENIED',
      'message', 'You do not have permission to create buses'
    );
  END IF;

  -- التحقق من صحة البيانات
  IF bus_plate_number IS NULL OR bus_plate_number = '' THEN
    RETURN json_build_object(
      'success', false,
      'error', 'VALIDATION_ERROR',
      'message', 'Plate number is required'
    );
  END IF;

  IF bus_capacity IS NULL OR bus_capacity <= 0 THEN
    RETURN json_build_object(
      'success', false,
      'error', 'VALIDATION_ERROR',
      'message', 'Valid capacity is required'
    );
  END IF;

  -- التحقق من عدم وجود حافلة بنفس رقم اللوحة
  IF EXISTS (SELECT 1 FROM buses WHERE plate_number = bus_plate_number) THEN
    RETURN json_build_object(
      'success', false,
      'error', 'PLATE_EXISTS',
      'message', 'A bus with this plate number already exists'
    );
  END IF;

  -- إنشاء معرف جديد للحافلة
  new_bus_id := gen_random_uuid();
  
  -- إدراج سجل الحافلة
  INSERT INTO buses (
    id,
    plate_number,
    capacity,
    tenant_id,
    driver_id,
    model,
    year,
    is_active,
    created_at,
    updated_at
  ) VALUES (
    new_bus_id,
    bus_plate_number,
    bus_capacity,
    bus_tenant_id,
    bus_driver_id,
    bus_model,
    bus_year,
    true,
    now(),
    now()
  );
  
  RETURN json_build_object(
    'success', true,
    'bus_id', new_bus_id,
    'message', 'Bus created successfully',
    'data', json_build_object(
      'id', new_bus_id,
      'plate_number', bus_plate_number,
      'capacity', bus_capacity,
      'tenant_id', bus_tenant_id
    )
  );
  
EXCEPTION
  WHEN OTHERS THEN
    RETURN json_build_object(
      'success', false,
      'error', 'DATABASE_ERROR',
      'message', 'Failed to create bus: ' || SQLERRM
    );
END;
$$;

-- دالة تحديث حافلة
CREATE OR REPLACE FUNCTION update_bus(
  target_bus_id uuid,
  bus_plate_number text DEFAULT NULL,
  bus_capacity integer DEFAULT NULL,
  bus_driver_id uuid DEFAULT NULL,
  bus_model text DEFAULT NULL,
  bus_year integer DEFAULT NULL,
  bus_is_active boolean DEFAULT NULL
)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result json;
  current_user_role text;
  current_tenant_id uuid;
  bus_tenant_id uuid;
BEGIN
  -- الحصول على معلومات المستخدم الحالي
  SELECT role::text, tenant_id INTO current_user_role, current_tenant_id 
  FROM users WHERE id = auth.uid();
  
  -- الحصول على معلومات الحافلة
  SELECT tenant_id INTO bus_tenant_id FROM buses WHERE id = target_bus_id;
  
  IF NOT FOUND THEN
    RETURN json_build_object(
      'success', false,
      'error', 'BUS_NOT_FOUND',
      'message', 'Bus not found'
    );
  END IF;
  
  -- التحقق من الصلاحيات
  IF NOT (
    current_user_role = 'admin' OR 
    (current_user_role IN ('school_manager', 'supervisor') AND current_tenant_id = bus_tenant_id)
  ) THEN
    RETURN json_build_object(
      'success', false,
      'error', 'PERMISSION_DENIED',
      'message', 'You do not have permission to update this bus'
    );
  END IF;

  -- تحديث البيانات
  UPDATE buses SET
    plate_number = COALESCE(bus_plate_number, plate_number),
    capacity = COALESCE(bus_capacity, capacity),
    driver_id = COALESCE(bus_driver_id, driver_id),
    model = COALESCE(bus_model, model),
    year = COALESCE(bus_year, year),
    is_active = COALESCE(bus_is_active, is_active),
    updated_at = now()
  WHERE id = target_bus_id;
  
  RETURN json_build_object(
    'success', true,
    'message', 'Bus updated successfully'
  );
  
EXCEPTION
  WHEN OTHERS THEN
    RETURN json_build_object(
      'success', false,
      'error', 'DATABASE_ERROR',
      'message', 'Failed to update bus: ' || SQLERRM
    );
END;
$$;

-- ===== دوال إدارة المسارات =====
-- Route Management Functions

-- دالة إنشاء مسار جديد
CREATE OR REPLACE FUNCTION create_route(
  route_name text,
  route_tenant_id uuid,
  route_bus_id uuid DEFAULT NULL,
  route_schedule jsonb DEFAULT '{}'::jsonb,
  route_stops jsonb DEFAULT '[]'::jsonb
)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  new_route_id uuid;
  result json;
  current_user_role text;
  current_tenant_id uuid;
BEGIN
  -- التحقق من الصلاحيات
  SELECT role::text, tenant_id INTO current_user_role, current_tenant_id 
  FROM users WHERE id = auth.uid();
  
  IF NOT (
    current_user_role = 'admin' OR 
    (current_user_role IN ('school_manager', 'supervisor') AND current_tenant_id = route_tenant_id)
  ) THEN
    RETURN json_build_object(
      'success', false,
      'error', 'PERMISSION_DENIED',
      'message', 'You do not have permission to create routes'
    );
  END IF;

  -- التحقق من صحة البيانات
  IF route_name IS NULL OR route_name = '' THEN
    RETURN json_build_object(
      'success', false,
      'error', 'VALIDATION_ERROR',
      'message', 'Route name is required'
    );
  END IF;

  -- إنشاء معرف جديد للمسار
  new_route_id := gen_random_uuid();
  
  -- إدراج سجل المسار
  INSERT INTO routes (
    id,
    name,
    tenant_id,
    bus_id,
    schedule,
    stops,
    is_active,
    created_at,
    updated_at
  ) VALUES (
    new_route_id,
    route_name,
    route_tenant_id,
    route_bus_id,
    route_schedule,
    route_stops,
    true,
    now(),
    now()
  );
  
  RETURN json_build_object(
    'success', true,
    'route_id', new_route_id,
    'message', 'Route created successfully',
    'data', json_build_object(
      'id', new_route_id,
      'name', route_name,
      'tenant_id', route_tenant_id,
      'bus_id', route_bus_id
    )
  );
  
EXCEPTION
  WHEN OTHERS THEN
    RETURN json_build_object(
      'success', false,
      'error', 'DATABASE_ERROR',
      'message', 'Failed to create route: ' || SQLERRM
    );
END;
$$;

-- منح الصلاحيات للدوال
GRANT EXECUTE ON FUNCTION create_school_tenant(text, text, text, text, jsonb) TO authenticated;
GRANT EXECUTE ON FUNCTION update_school_tenant(uuid, text, text, text, text, jsonb) TO authenticated;
GRANT EXECUTE ON FUNCTION create_bus(text, integer, uuid, uuid, text, integer) TO authenticated;
GRANT EXECUTE ON FUNCTION update_bus(uuid, text, integer, uuid, text, integer, boolean) TO authenticated;
GRANT EXECUTE ON FUNCTION create_route(text, uuid, uuid, jsonb, jsonb) TO authenticated;
