-- Comprehensive Database Fixes - إصل<PERSON><PERSON><PERSON><PERSON> قاعدة البيانات الشاملة
-- يحل جميع المشاكل المكتشفة في قاعدة البيانات

-- تفعيل Extensions المطلوبة
CREATE EXTENSION IF NOT EXISTS pgcrypto;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ===== 1. إضافة الأعمدة المفقودة =====

-- إضافة عمود notes للحافلات
ALTER TABLE buses ADD COLUMN IF NOT EXISTS notes TEXT;

-- إضافة عمود scheduled_date لصيانة الحافلات (إذا لم يكن موجوداً)
ALTER TABLE bus_maintenance ADD COLUMN IF NOT EXISTS scheduled_date DATE;

-- إضافة أعمدة إضافية للطلاب
ALTER TABLE students ADD COLUMN IF NOT EXISTS class VARCHAR(10);
ALTER TABLE students ADD COLUMN IF NOT EXISTS section VARCHAR(10);

-- ===== 2. إنشاء الجداول المفقودة =====

-- جدول تاريخ الصيانة
CREATE TABLE IF NOT EXISTS maintenance_history (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  bus_id UUID REFERENCES buses(id) ON DELETE CASCADE,
  maintenance_date DATE NOT NULL,
  maintenance_type VARCHAR(50) NOT NULL CHECK (maintenance_type IN ('routine', 'repair', 'inspection', 'emergency')),
  description TEXT,
  cost DECIMAL(10,2),
  mechanic_name VARCHAR(100),
  parts_used JSONB,
  status VARCHAR(20) DEFAULT 'completed' CHECK (status IN ('scheduled', 'in_progress', 'completed', 'cancelled')),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  tenant_id UUID REFERENCES tenants(id) NOT NULL
);

-- جدول سجل المعاملات
CREATE TABLE IF NOT EXISTS transaction_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  transaction_type VARCHAR(50) NOT NULL,
  success BOOLEAN NOT NULL,
  details JSONB,
  tenant_id UUID REFERENCES tenants(id),
  user_id UUID REFERENCES users(id),
  timestamp TIMESTAMP DEFAULT NOW()
);

-- جدول تحليلات الإشعارات
CREATE TABLE IF NOT EXISTS notification_analytics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  event_type VARCHAR(20) NOT NULL CHECK (event_type IN ('sent', 'delivered', 'clicked', 'failed')),
  notification_id UUID REFERENCES notifications(id) ON DELETE CASCADE,
  notification_type VARCHAR(20),
  tenant_id UUID REFERENCES tenants(id) NOT NULL,
  timestamp TIMESTAMP DEFAULT NOW(),
  metadata JSONB
);

-- جدول توصيل الإشعارات
CREATE TABLE IF NOT EXISTS notification_deliveries (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  notification_id UUID REFERENCES notifications(id) ON DELETE CASCADE,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  tenant_id UUID REFERENCES tenants(id) NOT NULL,
  status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'delivered', 'failed', 'read')),
  delivered_at TIMESTAMP,
  read_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW()
);

-- جدول tokens الـ Push Notifications
CREATE TABLE IF NOT EXISTS push_tokens (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  token TEXT NOT NULL,
  platform VARCHAR(10) CHECK (platform IN ('ios', 'android', 'web')),
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(user_id, token)
);

-- جدول سجل الأمان
CREATE TABLE IF NOT EXISTS security_audit_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id),
  user_role VARCHAR(20),
  resource_type VARCHAR(50),
  action VARCHAR(50),
  allowed BOOLEAN NOT NULL,
  reason TEXT,
  ip_address INET,
  user_agent TEXT,
  timestamp TIMESTAMP DEFAULT NOW(),
  tenant_id UUID REFERENCES tenants(id)
);

-- ===== 3. إضافة الفهارس المحسنة =====

-- فهارس للطلاب
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_students_tenant_grade
ON students(tenant_id, grade) WHERE is_active = true;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_students_parent_id
ON students(parent_id) WHERE parent_id IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_students_route_stop
ON students(route_stop_id) WHERE route_stop_id IS NOT NULL;

-- فهارس للحضور
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_attendance_date_student
ON attendance(recorded_at, student_id, tenant_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_attendance_status
ON attendance(status, tenant_id, recorded_at);

-- فهارس للحافلات
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_buses_tenant_active
ON buses(tenant_id, is_active) WHERE is_active = true;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_buses_driver
ON buses(driver_id) WHERE driver_id IS NOT NULL;

-- فهارس للإشعارات
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_notifications_user_unread
ON notifications(user_id, created_at) WHERE read = false;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_notifications_tenant_type
ON notifications(tenant_id, type, created_at);

-- فهارس للجداول الجديدة
CREATE INDEX IF NOT EXISTS idx_maintenance_history_bus_date
ON maintenance_history(bus_id, maintenance_date DESC);

CREATE INDEX IF NOT EXISTS idx_notification_deliveries_user
ON notification_deliveries(user_id, status, created_at);

CREATE INDEX IF NOT EXISTS idx_push_tokens_user_active
ON push_tokens(user_id) WHERE is_active = true;

CREATE INDEX IF NOT EXISTS idx_security_audit_user_time
ON security_audit_logs(user_id, timestamp DESC);

-- ===== 4. إضافة القيود والتحققات =====

-- قيود unique
ALTER TABLE students
ADD CONSTRAINT IF NOT EXISTS unique_student_id_per_tenant
UNIQUE (student_id, tenant_id);

ALTER TABLE buses
ADD CONSTRAINT IF NOT EXISTS unique_plate_number_per_tenant
UNIQUE (plate_number, tenant_id);

-- قيود check
ALTER TABLE buses
ADD CONSTRAINT IF NOT EXISTS check_capacity
CHECK (capacity > 0 AND capacity <= 100);

ALTER TABLE buses
ADD CONSTRAINT IF NOT EXISTS check_plate_number_format
CHECK (plate_number ~ '^[A-Z]{1,3}-[0-9]{1,4}$');

-- قيود foreign key مع cascade
ALTER TABLE attendance
DROP CONSTRAINT IF EXISTS fk_attendance_student,
ADD CONSTRAINT fk_attendance_student
FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE;

-- ===== 5. إضافة الدوال المساعدة =====

-- دالة للتحقق من تكرار رقم الطالب
CREATE OR REPLACE FUNCTION check_unique_student_id(
  p_student_id VARCHAR,
  p_tenant_id UUID,
  p_exclude_id UUID DEFAULT NULL
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN NOT EXISTS (
    SELECT 1 FROM students
    WHERE student_id = p_student_id
    AND tenant_id = p_tenant_id
    AND (p_exclude_id IS NULL OR id != p_exclude_id)
    AND is_active = true
  );
END;
$$;

-- دالة للتحقق من سعة الحافلة
CREATE OR REPLACE FUNCTION check_bus_capacity(
  p_bus_id UUID,
  p_additional_students INTEGER DEFAULT 1
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  bus_capacity INTEGER;
  current_students INTEGER;
BEGIN
  -- الحصول على سعة الحافلة
  SELECT capacity INTO bus_capacity
  FROM buses WHERE id = p_bus_id AND is_active = true;

  IF bus_capacity IS NULL THEN
    RETURN false;
  END IF;

  -- حساب عدد الطلاب الحاليين
  SELECT COUNT(*) INTO current_students
  FROM students s
  JOIN route_stops rs ON s.route_stop_id = rs.id
  JOIN routes r ON rs.route_id = r.id
  WHERE r.bus_id = p_bus_id AND s.is_active = true;

  -- التحقق من السعة
  RETURN (current_students + p_additional_students) <= bus_capacity;
END;
$$;

-- دالة للتحقق من تعيين السائق
CREATE OR REPLACE FUNCTION check_driver_assignment(
  p_driver_id UUID,
  p_exclude_bus_id UUID DEFAULT NULL
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN NOT EXISTS (
    SELECT 1 FROM buses
    WHERE driver_id = p_driver_id
    AND is_active = true
    AND (p_exclude_bus_id IS NULL OR id != p_exclude_bus_id)
  );
END;
$$;

-- دالة للتحقق من علاقة ولي الأمر بالطالب
CREATE OR REPLACE FUNCTION check_parent_student_relation(
  p_parent_id UUID,
  p_student_id UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM students
    WHERE id = p_student_id
    AND parent_id = p_parent_id
    AND is_active = true
  );
END;
$$;

-- دالة للحصول على إحصائيات الإشعارات
CREATE OR REPLACE FUNCTION get_notification_analytics(
  p_tenant_id UUID,
  p_date_from TIMESTAMP DEFAULT NULL,
  p_date_to TIMESTAMP DEFAULT NULL
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result JSON;
  date_from TIMESTAMP;
  date_to TIMESTAMP;
BEGIN
  -- تعيين التواريخ الافتراضية
  date_from := COALESCE(p_date_from, NOW() - INTERVAL '30 days');
  date_to := COALESCE(p_date_to, NOW());

  SELECT json_build_object(
    'sent', COALESCE(SUM(CASE WHEN event_type = 'sent' THEN 1 ELSE 0 END), 0),
    'delivered', COALESCE(SUM(CASE WHEN event_type = 'delivered' THEN 1 ELSE 0 END), 0),
    'clicked', COALESCE(SUM(CASE WHEN event_type = 'clicked' THEN 1 ELSE 0 END), 0),
    'failed', COALESCE(SUM(CASE WHEN event_type = 'failed' THEN 1 ELSE 0 END), 0)
  ) INTO result
  FROM notification_analytics
  WHERE tenant_id = p_tenant_id
  AND timestamp BETWEEN date_from AND date_to;

  RETURN COALESCE(result, '{"sent":0,"delivered":0,"clicked":0,"failed":0}'::json);
END;
$$;

-- ===== 6. تحسين سياسات RLS =====

-- تفعيل RLS على الجداول الجديدة
ALTER TABLE maintenance_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE transaction_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE notification_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE notification_deliveries ENABLE ROW LEVEL SECURITY;
ALTER TABLE push_tokens ENABLE ROW LEVEL SECURITY;
ALTER TABLE security_audit_logs ENABLE ROW LEVEL SECURITY;

-- سياسات محسنة للحضور
DROP POLICY IF EXISTS "Users can access attendance" ON public.attendance;

CREATE POLICY "Users can access attendance optimized"
ON public.attendance
FOR ALL
TO authenticated
USING (
  -- Admin access
  public.is_admin() OR
  -- Direct user access (for drivers recording attendance)
  recorded_by = auth.uid() OR
  -- Parent access (optimized with index)
  EXISTS (
    SELECT 1 FROM public.students s
    WHERE s.id = attendance.student_id
    AND s.parent_id = auth.uid()
  ) OR
  -- Tenant access (optimized with index)
  tenant_id = public.get_user_tenant_id()
);

-- سياسات للجداول الجديدة
CREATE POLICY "Users can access maintenance history"
ON maintenance_history
FOR ALL
TO authenticated
USING (
  public.is_admin() OR
  tenant_id = public.get_user_tenant_id()
);

CREATE POLICY "Users can access transaction logs"
ON transaction_logs
FOR SELECT
TO authenticated
USING (
  public.is_admin() OR
  tenant_id = public.get_user_tenant_id()
);

CREATE POLICY "Users can access notification analytics"
ON notification_analytics
FOR ALL
TO authenticated
USING (
  public.is_admin() OR
  tenant_id = public.get_user_tenant_id()
);

CREATE POLICY "Users can access notification deliveries"
ON notification_deliveries
FOR ALL
TO authenticated
USING (
  public.is_admin() OR
  user_id = auth.uid() OR
  tenant_id = public.get_user_tenant_id()
);

CREATE POLICY "Users can manage push tokens"
ON push_tokens
FOR ALL
TO authenticated
USING (
  user_id = auth.uid()
);

CREATE POLICY "Admins can access security audit logs"
ON security_audit_logs
FOR SELECT
TO authenticated
USING (
  public.is_admin()
);

-- ===== 7. منح الصلاحيات =====

-- منح صلاحيات تنفيذ الدوال
GRANT EXECUTE ON FUNCTION check_unique_student_id(VARCHAR, UUID, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION check_bus_capacity(UUID, INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION check_driver_assignment(UUID, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION check_parent_student_relation(UUID, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_notification_analytics(UUID, TIMESTAMP, TIMESTAMP) TO authenticated;

-- منح صلاحيات الجداول
GRANT SELECT, INSERT, UPDATE, DELETE ON maintenance_history TO authenticated;
GRANT SELECT ON transaction_logs TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON notification_analytics TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON notification_deliveries TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON push_tokens TO authenticated;
GRANT SELECT ON security_audit_logs TO authenticated;

-- ===== 8. إضافة Triggers للتحديث التلقائي =====

-- Trigger لتحديث updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- تطبيق Trigger على الجداول المناسبة
DROP TRIGGER IF EXISTS update_maintenance_history_updated_at ON maintenance_history;
CREATE TRIGGER update_maintenance_history_updated_at
    BEFORE UPDATE ON maintenance_history
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_push_tokens_updated_at ON push_tokens;
CREATE TRIGGER update_push_tokens_updated_at
    BEFORE UPDATE ON push_tokens
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- ===== 9. إضافة Views مفيدة =====

-- View للحصول على إحصائيات الحافلات
CREATE OR REPLACE VIEW bus_statistics AS
SELECT
  b.id,
  b.plate_number,
  b.capacity,
  b.tenant_id,
  COUNT(DISTINCT s.id) as current_students,
  (b.capacity - COUNT(DISTINCT s.id)) as available_capacity,
  ROUND((COUNT(DISTINCT s.id)::DECIMAL / b.capacity) * 100, 2) as occupancy_rate
FROM buses b
LEFT JOIN routes r ON b.id = r.bus_id
LEFT JOIN route_stops rs ON r.id = rs.route_id
LEFT JOIN students s ON rs.id = s.route_stop_id AND s.is_active = true
WHERE b.is_active = true
GROUP BY b.id, b.plate_number, b.capacity, b.tenant_id;

-- View للحصول على إحصائيات الحضور
CREATE OR REPLACE VIEW attendance_statistics AS
SELECT
  s.tenant_id,
  s.grade,
  DATE(a.recorded_at) as attendance_date,
  COUNT(*) as total_records,
  COUNT(CASE WHEN a.status = 'present' THEN 1 END) as present_count,
  COUNT(CASE WHEN a.status = 'absent' THEN 1 END) as absent_count,
  ROUND((COUNT(CASE WHEN a.status = 'present' THEN 1 END)::DECIMAL / COUNT(*)) * 100, 2) as attendance_rate
FROM attendance a
JOIN students s ON a.student_id = s.id
WHERE s.is_active = true
GROUP BY s.tenant_id, s.grade, DATE(a.recorded_at);

-- منح صلاحيات Views
GRANT SELECT ON bus_statistics TO authenticated;
GRANT SELECT ON attendance_statistics TO authenticated;

-- ===== 10. تعليقات للتوثيق =====

COMMENT ON TABLE maintenance_history IS 'تاريخ صيانة الحافلات مع التفاصيل والتكاليف';
COMMENT ON TABLE transaction_logs IS 'سجل المعاملات المعقدة في النظام';
COMMENT ON TABLE notification_analytics IS 'تحليلات الإشعارات ومعدلات التفاعل';
COMMENT ON TABLE notification_deliveries IS 'تتبع توصيل الإشعارات للمستخدمين';
COMMENT ON TABLE push_tokens IS 'رموز الإشعارات الفورية للأجهزة';
COMMENT ON TABLE security_audit_logs IS 'سجل مراجعة الأمان ومحاولات الوصول';

COMMENT ON FUNCTION check_unique_student_id IS 'التحقق من عدم تكرار رقم الطالب في المؤسسة';
COMMENT ON FUNCTION check_bus_capacity IS 'التحقق من سعة الحافلة قبل إضافة طلاب';
COMMENT ON FUNCTION check_driver_assignment IS 'التحقق من عدم تعيين السائق لحافلة أخرى';
COMMENT ON FUNCTION check_parent_student_relation IS 'التحقق من علاقة ولي الأمر بالطالب';
COMMENT ON FUNCTION get_notification_analytics IS 'الحصول على إحصائيات الإشعارات لفترة محددة';

-- ===== 11. إصلاح دالة create_student_with_user =====

CREATE OR REPLACE FUNCTION create_student_with_user(
  student_email text,
  student_password text,
  student_name text,
  student_grade text,
  student_tenant_id uuid,
  parent_id uuid DEFAULT NULL,
  route_stop_id uuid DEFAULT NULL
)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
DECLARE
  new_user_id uuid;
  new_student_id uuid;
  result json;
BEGIN
  -- Generate new UUID
  new_user_id := gen_random_uuid();

  -- Insert into auth.users
  INSERT INTO auth.users (
    id,
    email,
    encrypted_password,
    email_confirmed_at,
    raw_app_meta_data,
    raw_user_meta_data,
    created_at,
    updated_at,
    role,
    aud,
    confirmation_token
  ) VALUES (
    new_user_id,
    student_email,
    crypt(student_password, gen_salt('bf')),
    now(),
    '{"provider":"email","providers":["email"]}',
    json_build_object('name', student_name, 'role', 'student'),
    now(),
    now(),
    'authenticated',
    'authenticated',
    ''
  );

  -- Insert into public.users
  INSERT INTO users (
    id,
    email,
    name,
    role,
    tenant_id,
    is_active,
    created_at,
    updated_at
  ) VALUES (
    new_user_id,
    student_email,
    student_name,
    'student'::user_role,
    student_tenant_id,
    true,
    now(),
    now()
  );

  -- Insert into students
  INSERT INTO students (
    id,
    name,
    grade,
    tenant_id,
    parent_id,
    route_stop_id,
    is_active,
    created_at,
    updated_at
  ) VALUES (
    gen_random_uuid(),
    student_name,
    student_grade,
    student_tenant_id,
    parent_id,
    route_stop_id,
    true,
    now(),
    now()
  ) RETURNING id INTO new_student_id;

  -- Return success
  result := json_build_object(
    'success', true,
    'user_id', new_user_id,
    'student_id', new_student_id,
    'message', 'Student created successfully'
  );

  RETURN result;

EXCEPTION
  WHEN OTHERS THEN
    result := json_build_object(
      'success', false,
      'error', SQLERRM,
      'message', 'Failed to create student'
    );
    RETURN result;
END;
$$;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION create_student_with_user(text, text, text, text, uuid, uuid, uuid) TO authenticated;

COMMENT ON FUNCTION create_student_with_user IS 'إنشاء طالب جديد مع حساب مستخدم في النظام';
