-- ===================================================================
-- المرحلة الأولى: إكمال سياسات RLS للجداول المتبقية
-- Phase 1: Complete RLS Policies for Remaining Tables
-- Generated: 2025-01-30
-- ===================================================================

-- ===== 1. سياسات جدول الحافلات =====
-- Buses table policies

-- سياسة القراءة للحافلات
CREATE POLICY "buses_read_policy" ON public.buses
FOR SELECT TO authenticated
USING (
  public.check_permission(
    auth.uid(),
    'bus',
    'read',
    tenant_id,
    driver_id,
    jsonb_build_object('resource_id', id)
  )
);

-- سياسة التحديث للحافلات
CREATE POLICY "buses_update_policy" ON public.buses
FOR UPDATE TO authenticated
USING (
  public.check_permission(
    auth.uid(),
    'bus',
    'update',
    tenant_id,
    driver_id,
    jsonb_build_object('resource_id', id)
  )
)
WITH CHECK (
  public.check_permission(
    auth.uid(),
    'bus',
    'update',
    tenant_id,
    driver_id,
    jsonb_build_object('resource_id', id)
  )
);

-- سياسة الإنشاء للحافلات
CREATE POLICY "buses_create_policy" ON public.buses
FOR INSERT TO authenticated
WITH CHECK (
  public.check_permission(
    auth.uid(),
    'bus',
    'create',
    tenant_id,
    null,
    jsonb_build_object('target_tenant_id', tenant_id)
  )
);

-- سياسة الحذف للحافلات
CREATE POLICY "buses_delete_policy" ON public.buses
FOR DELETE TO authenticated
USING (
  public.check_permission(
    auth.uid(),
    'bus',
    'delete',
    tenant_id,
    driver_id,
    jsonb_build_object('resource_id', id)
  )
);

-- ===== 2. سياسات جدول المسارات =====
-- Routes table policies

-- سياسة القراءة للمسارات
CREATE POLICY "routes_read_policy" ON public.routes
FOR SELECT TO authenticated
USING (
  public.check_permission(
    auth.uid(),
    'route',
    'read',
    tenant_id,
    null,
    jsonb_build_object('resource_id', id)
  )
);

-- سياسة التحديث للمسارات
CREATE POLICY "routes_update_policy" ON public.routes
FOR UPDATE TO authenticated
USING (
  public.check_permission(
    auth.uid(),
    'route',
    'update',
    tenant_id,
    null,
    jsonb_build_object('resource_id', id)
  )
)
WITH CHECK (
  public.check_permission(
    auth.uid(),
    'route',
    'update',
    tenant_id,
    null,
    jsonb_build_object('resource_id', id)
  )
);

-- سياسة الإنشاء للمسارات
CREATE POLICY "routes_create_policy" ON public.routes
FOR INSERT TO authenticated
WITH CHECK (
  public.check_permission(
    auth.uid(),
    'route',
    'create',
    tenant_id,
    null,
    jsonb_build_object('target_tenant_id', tenant_id)
  )
);

-- سياسة الحذف للمسارات
CREATE POLICY "routes_delete_policy" ON public.routes
FOR DELETE TO authenticated
USING (
  public.check_permission(
    auth.uid(),
    'route',
    'delete',
    tenant_id,
    null,
    jsonb_build_object('resource_id', id)
  )
);

-- ===== 3. سياسات جدول الطلاب =====
-- Students table policies

-- سياسة القراءة للطلاب
CREATE POLICY "students_read_policy" ON public.students
FOR SELECT TO authenticated
USING (
  public.check_permission(
    auth.uid(),
    'student',
    'read',
    tenant_id,
    parent_id,
    jsonb_build_object('resource_id', id)
  )
);

-- سياسة التحديث للطلاب
CREATE POLICY "students_update_policy" ON public.students
FOR UPDATE TO authenticated
USING (
  public.check_permission(
    auth.uid(),
    'student',
    'update',
    tenant_id,
    parent_id,
    jsonb_build_object('resource_id', id)
  )
)
WITH CHECK (
  public.check_permission(
    auth.uid(),
    'student',
    'update',
    tenant_id,
    parent_id,
    jsonb_build_object('resource_id', id)
  )
);

-- سياسة الإنشاء للطلاب
CREATE POLICY "students_create_policy" ON public.students
FOR INSERT TO authenticated
WITH CHECK (
  public.check_permission(
    auth.uid(),
    'student',
    'create',
    tenant_id,
    null,
    jsonb_build_object('target_tenant_id', tenant_id)
  )
);

-- سياسة الحذف للطلاب
CREATE POLICY "students_delete_policy" ON public.students
FOR DELETE TO authenticated
USING (
  public.check_permission(
    auth.uid(),
    'student',
    'delete',
    tenant_id,
    parent_id,
    jsonb_build_object('resource_id', id)
  )
);

-- ===== 4. سياسات جدول الحضور =====
-- Attendance table policies

-- سياسة القراءة للحضور
CREATE POLICY "attendance_read_policy" ON public.attendance
FOR SELECT TO authenticated
USING (
  public.check_permission(
    auth.uid(),
    'attendance',
    'read',
    tenant_id,
    recorded_by,
    jsonb_build_object('resource_id', id, 'student_id', student_id)
  )
);

-- سياسة التحديث للحضور
CREATE POLICY "attendance_update_policy" ON public.attendance
FOR UPDATE TO authenticated
USING (
  public.check_permission(
    auth.uid(),
    'attendance',
    'update',
    tenant_id,
    recorded_by,
    jsonb_build_object('resource_id', id, 'student_id', student_id)
  )
)
WITH CHECK (
  public.check_permission(
    auth.uid(),
    'attendance',
    'update',
    tenant_id,
    recorded_by,
    jsonb_build_object('resource_id', id, 'student_id', student_id)
  )
);

-- سياسة الإنشاء للحضور
CREATE POLICY "attendance_create_policy" ON public.attendance
FOR INSERT TO authenticated
WITH CHECK (
  public.check_permission(
    auth.uid(),
    'attendance',
    'create',
    tenant_id,
    null,
    jsonb_build_object('target_tenant_id', tenant_id, 'student_id', student_id)
  )
);

-- سياسة الحذف للحضور
CREATE POLICY "attendance_delete_policy" ON public.attendance
FOR DELETE TO authenticated
USING (
  public.check_permission(
    auth.uid(),
    'attendance',
    'delete',
    tenant_id,
    recorded_by,
    jsonb_build_object('resource_id', id, 'student_id', student_id)
  )
);

-- ===== 5. سياسات جدول الإشعارات =====
-- Notifications table policies

-- سياسة القراءة للإشعارات
CREATE POLICY "notifications_read_policy" ON public.notifications
FOR SELECT TO authenticated
USING (
  public.is_system_admin() OR 
  user_id = auth.uid() OR 
  tenant_id = public.get_user_tenant_secure()
);

-- سياسة التحديث للإشعارات
CREATE POLICY "notifications_update_policy" ON public.notifications
FOR UPDATE TO authenticated
USING (
  public.is_system_admin() OR 
  user_id = auth.uid()
)
WITH CHECK (
  public.is_system_admin() OR 
  user_id = auth.uid()
);

-- سياسة الإنشاء للإشعارات
CREATE POLICY "notifications_create_policy" ON public.notifications
FOR INSERT TO authenticated
WITH CHECK (
  public.is_system_admin() OR 
  public.get_user_role_secure() IN ('school_manager', 'supervisor')
);

-- سياسة الحذف للإشعارات
CREATE POLICY "notifications_delete_policy" ON public.notifications
FOR DELETE TO authenticated
USING (
  public.is_system_admin() OR 
  user_id = auth.uid()
);

-- ===== 6. سياسات جدول صيانة الحافلات =====
-- Bus maintenance table policies

-- سياسة القراءة لصيانة الحافلات
CREATE POLICY "bus_maintenance_read_policy" ON public.bus_maintenance
FOR SELECT TO authenticated
USING (
  public.is_system_admin() OR 
  tenant_id = public.get_user_tenant_secure()
);

-- سياسة التحديث لصيانة الحافلات
CREATE POLICY "bus_maintenance_update_policy" ON public.bus_maintenance
FOR UPDATE TO authenticated
USING (
  public.is_system_admin() OR 
  (tenant_id = public.get_user_tenant_secure() AND 
   public.get_user_role_secure() IN ('school_manager', 'supervisor'))
)
WITH CHECK (
  public.is_system_admin() OR 
  (tenant_id = public.get_user_tenant_secure() AND 
   public.get_user_role_secure() IN ('school_manager', 'supervisor'))
);

-- سياسة الإنشاء لصيانة الحافلات
CREATE POLICY "bus_maintenance_create_policy" ON public.bus_maintenance
FOR INSERT TO authenticated
WITH CHECK (
  public.is_system_admin() OR 
  (tenant_id = public.get_user_tenant_secure() AND 
   public.get_user_role_secure() IN ('school_manager', 'supervisor'))
);

-- سياسة الحذف لصيانة الحافلات
CREATE POLICY "bus_maintenance_delete_policy" ON public.bus_maintenance
FOR DELETE TO authenticated
USING (
  public.is_system_admin() OR 
  (tenant_id = public.get_user_tenant_secure() AND 
   public.get_user_role_secure() IN ('school_manager', 'supervisor'))
);

-- تسجيل إتمام جميع السياسات
SELECT public.log_security_event(
  'ALL_RLS_POLICIES_COMPLETED',
  'INFO',
  'Phase 1 all RLS policies implementation completed successfully',
  auth.uid(),
  null,
  jsonb_build_object(
    'phase', 1,
    'action', 'complete_rls_policies',
    'total_policies_created', 28,
    'tables_secured', 8
  )
);
