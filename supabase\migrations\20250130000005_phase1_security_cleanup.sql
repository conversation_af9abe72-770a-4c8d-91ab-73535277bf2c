-- ===================================================================
-- المرحلة الأولى: تنظيف نظام الأمان والصلاحيات
-- Phase 1: Security System Cleanup
-- Generated: 2025-01-30
-- ===================================================================

-- ===== 1. إزالة جميع السياسات المتضاربة والمكررة =====
-- Remove all conflicting and duplicate policies

-- إزالة سياسات جدول المستخدمين المتضاربة
DROP POLICY IF EXISTS "admin_full_access_users" ON public.users;
DROP POLICY IF EXISTS "admin_users_full_access" ON public.users;
DROP POLICY IF EXISTS "school_manager_users_access" ON public.users;
DROP POLICY IF EXISTS "supervisor_users_view" ON public.users;
DROP POLICY IF EXISTS "users_own_profile_access" ON public.users;
DROP POLICY IF EXISTS "Admin users can access all users" ON public.users;
DROP POLICY IF EXISTS "Users can view their own profile" ON public.users;
DROP POLICY IF EXISTS "Users can update their own profile" ON public.users;
DROP POLICY IF EXISTS "Admin users can view all users" ON public.users;
DROP POLICY IF EXISTS "Admin users can update all users" ON public.users;
DROP POLICY IF EXISTS "School managers can view users in their tenant" ON public.users;
DROP POLICY IF EXISTS "School managers can update users in their tenant" ON public.users;
DROP POLICY IF EXISTS "Admin users can insert users" ON public.users;
DROP POLICY IF EXISTS "Admin users can delete users" ON public.users;

-- إزالة سياسات جدول الحافلات المتضاربة
DROP POLICY IF EXISTS "admin_buses_full_access" ON public.buses;
DROP POLICY IF EXISTS "admin_full_access_buses" ON public.buses;
DROP POLICY IF EXISTS "drivers_assigned_buses_access" ON public.buses;
DROP POLICY IF EXISTS "school_staff_buses_access" ON public.buses;
DROP POLICY IF EXISTS "tenant_buses_view_only" ON public.buses;
DROP POLICY IF EXISTS "Admin can access all buses" ON public.buses;
DROP POLICY IF EXISTS "Tenant users can view their buses" ON public.buses;
DROP POLICY IF EXISTS "Users can access buses" ON public.buses;

-- إزالة سياسات جدول الطلاب المتضاربة
DROP POLICY IF EXISTS "admin_full_access_students" ON public.students;
DROP POLICY IF EXISTS "admin_students_full_access" ON public.students;
DROP POLICY IF EXISTS "drivers_route_students_view" ON public.students;
DROP POLICY IF EXISTS "parents_children_view" ON public.students;
DROP POLICY IF EXISTS "school_staff_students_access" ON public.students;
DROP POLICY IF EXISTS "students_own_profile_view" ON public.students;
DROP POLICY IF EXISTS "Admin can access all students" ON public.students;
DROP POLICY IF EXISTS "Tenant users can view their students" ON public.students;
DROP POLICY IF EXISTS "Users can access students" ON public.students;

-- إزالة سياسات جدول المسارات المتضاربة
DROP POLICY IF EXISTS "admin_full_access_routes" ON public.routes;
DROP POLICY IF EXISTS "admin_routes_full_access" ON public.routes;
DROP POLICY IF EXISTS "school_staff_routes_access" ON public.routes;
DROP POLICY IF EXISTS "tenant_routes_view_only" ON public.routes;
DROP POLICY IF EXISTS "Admin can access all routes" ON public.routes;
DROP POLICY IF EXISTS "Tenant users can view their routes" ON public.routes;
DROP POLICY IF EXISTS "Users can access routes" ON public.routes;

-- إزالة سياسات جدول المدارس المتضاربة
DROP POLICY IF EXISTS "admin_full_access_tenants" ON public.tenants;
DROP POLICY IF EXISTS "admin_tenants_full_access" ON public.tenants;
DROP POLICY IF EXISTS "school_manager_own_tenant_access" ON public.tenants;
DROP POLICY IF EXISTS "users_own_tenant_view_only" ON public.tenants;
DROP POLICY IF EXISTS "Admin can access all schools" ON public.tenants;
DROP POLICY IF EXISTS "Users can view their tenant" ON public.tenants;
DROP POLICY IF EXISTS "Users can access tenants" ON public.tenants;

-- إزالة سياسات جدول الحضور المتضاربة
DROP POLICY IF EXISTS "admin_attendance_full_access" ON public.attendance;
DROP POLICY IF EXISTS "admin_full_access_attendance" ON public.attendance;
DROP POLICY IF EXISTS "parents_children_attendance_view" ON public.attendance;
DROP POLICY IF EXISTS "school_staff_attendance_access" ON public.attendance;
DROP POLICY IF EXISTS "Admin can access all attendance" ON public.attendance;
DROP POLICY IF EXISTS "Users can access attendance" ON public.attendance;

-- إزالة سياسات جدول الإشعارات المتضاربة
DROP POLICY IF EXISTS "admin_full_access" ON public.notifications;
DROP POLICY IF EXISTS "tenant_notifications" ON public.notifications;
DROP POLICY IF EXISTS "user_own_notifications" ON public.notifications;
DROP POLICY IF EXISTS "Admin can access all notifications" ON public.notifications;
DROP POLICY IF EXISTS "Users can access notifications" ON public.notifications;

-- إزالة سياسات جدول صيانة الحافلات المتضاربة
DROP POLICY IF EXISTS "Admin can access all maintenance" ON public.bus_maintenance;
DROP POLICY IF EXISTS "Admin can manage all bus maintenance" ON public.bus_maintenance;
DROP POLICY IF EXISTS "Admin can view all bus maintenance" ON public.bus_maintenance;
DROP POLICY IF EXISTS "Users can access bus maintenance" ON public.bus_maintenance;

-- ===== 2. إزالة الدوال المساعدة المتضاربة =====
-- Remove conflicting helper functions

-- إزالة الدوال المكررة للتحقق من الأدمن
DROP FUNCTION IF EXISTS public.auth_is_admin();
DROP FUNCTION IF EXISTS public.is_admin();
DROP FUNCTION IF EXISTS public.is_admin(uuid);
DROP FUNCTION IF EXISTS public.is_admin_user();
DROP FUNCTION IF EXISTS public.is_admin_user(uuid);

-- إزالة الدوال المكررة للحصول على دور المستخدم
DROP FUNCTION IF EXISTS public.auth_user_role();
DROP FUNCTION IF EXISTS public.get_current_user_role();
DROP FUNCTION IF EXISTS public.get_user_role();
DROP FUNCTION IF EXISTS public.get_user_role(uuid);

-- إزالة الدوال المكررة للحصول على معرف المستأجر
DROP FUNCTION IF EXISTS public.auth_user_tenant_id();
DROP FUNCTION IF EXISTS public.get_current_user_tenant_id();
DROP FUNCTION IF EXISTS public.get_user_tenant_id();
DROP FUNCTION IF EXISTS public.get_user_tenant_id(uuid);

-- إزالة دوال أخرى غير مستخدمة أو متضاربة
DROP FUNCTION IF EXISTS public.addauth();
DROP FUNCTION IF EXISTS public.checkauth();
DROP FUNCTION IF EXISTS public.checkauthtrigger();

-- ===== 3. تعطيل RLS مؤقتاً لإعادة البناء =====
-- Temporarily disable RLS for rebuilding

ALTER TABLE public.users DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.tenants DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.buses DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.routes DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.students DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.attendance DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.bus_maintenance DISABLE ROW LEVEL SECURITY;

-- تسجيل إتمام مرحلة التنظيف
INSERT INTO public.security_events (
  event_type,
  severity,
  description,
  metadata
) VALUES (
  'SECURITY_CLEANUP',
  'INFO',
  'Phase 1 security cleanup completed - removed conflicting policies and functions',
  jsonb_build_object(
    'phase', 1,
    'action', 'cleanup',
    'timestamp', now()
  )
);

COMMENT ON SCHEMA public IS 'Phase 1 Security Cleanup Completed - Ready for Centralized Permission System';
