-- Quick Fix for gen_salt function issue
-- تطبيق سريع لحل مشكلة دالة gen_salt

-- 1. تفعيل pgcrypto extension
CREATE EXTENSION IF NOT EXISTS pgcrypto;

-- 2. إصلاح دالة create_student_with_user
CREATE OR REPLACE FUNCTION create_student_with_user(
  student_email text,
  student_password text,
  student_name text,
  student_grade text,
  student_tenant_id uuid,
  parent_id uuid DEFAULT NULL,
  route_stop_id uuid DEFAULT NULL
)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
DECLARE
  new_user_id uuid;
  new_student_id uuid;
  result json;
BEGIN
  -- Generate new UUID
  new_user_id := gen_random_uuid();
  
  -- Insert into auth.users
  INSERT INTO auth.users (
    id,
    email,
    encrypted_password,
    email_confirmed_at,
    raw_app_meta_data,
    raw_user_meta_data,
    created_at,
    updated_at,
    role,
    aud,
    confirmation_token
  ) VALUES (
    new_user_id,
    student_email,
    crypt(student_password, gen_salt('bf')),
    now(),
    '{"provider":"email","providers":["email"]}',
    json_build_object('name', student_name, 'role', 'student'),
    now(),
    now(),
    'authenticated',
    'authenticated',
    ''
  );

  -- Insert into public.users
  INSERT INTO users (
    id,
    email,
    name,
    role,
    tenant_id,
    is_active,
    created_at,
    updated_at
  ) VALUES (
    new_user_id,
    student_email,
    student_name,
    'student'::user_role,
    student_tenant_id,
    true,
    now(),
    now()
  );

  -- Insert into students
  INSERT INTO students (
    id,
    name,
    grade,
    tenant_id,
    parent_id,
    route_stop_id,
    is_active,
    created_at,
    updated_at
  ) VALUES (
    gen_random_uuid(),
    student_name,
    student_grade,
    student_tenant_id,
    parent_id,
    route_stop_id,
    true,
    now(),
    now()
  ) RETURNING id INTO new_student_id;

  -- Return success
  result := json_build_object(
    'success', true,
    'user_id', new_user_id,
    'student_id', new_student_id,
    'message', 'Student created successfully'
  );

  RETURN result;

EXCEPTION
  WHEN OTHERS THEN
    result := json_build_object(
      'success', false,
      'error', SQLERRM,
      'message', 'Failed to create student'
    );
    RETURN result;
END;
$$;

-- 3. منح الصلاحيات
GRANT EXECUTE ON FUNCTION create_student_with_user(text, text, text, text, uuid, uuid, uuid) TO authenticated;

-- 4. إضافة عمود student_id إذا لم يكن موجوداً
ALTER TABLE students ADD COLUMN IF NOT EXISTS student_id VARCHAR(50);

-- 5. تحديث student_id للطلاب الموجودين
UPDATE students 
SET student_id = COALESCE(student_id, 'STU-' || EXTRACT(YEAR FROM created_at) || '-' || LPAD(ROW_NUMBER() OVER (ORDER BY created_at)::text, 4, '0'))
WHERE student_id IS NULL OR student_id = '';

-- 6. إضافة قيد unique لـ student_id
DO $$ 
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'unique_student_id_per_tenant') THEN
    ALTER TABLE students ADD CONSTRAINT unique_student_id_per_tenant UNIQUE (student_id, tenant_id);
  END IF;
END $$;

-- 7. التحقق من نجاح التطبيق
SELECT 
  'pgcrypto extension' as component,
  CASE WHEN EXISTS (SELECT 1 FROM pg_extension WHERE extname = 'pgcrypto') 
    THEN 'Enabled ✅' 
    ELSE 'Not Found ❌' 
  END as status
UNION ALL
SELECT 
  'create_student_with_user function' as component,
  CASE WHEN EXISTS (SELECT 1 FROM information_schema.routines WHERE routine_name = 'create_student_with_user') 
    THEN 'Created ✅' 
    ELSE 'Not Found ❌' 
  END as status
UNION ALL
SELECT 
  'student_id column' as component,
  CASE WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'students' AND column_name = 'student_id') 
    THEN 'Added ✅' 
    ELSE 'Missing ❌' 
  END as status;
