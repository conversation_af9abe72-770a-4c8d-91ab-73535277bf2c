-- Ultra Simple Student Creation Fix
-- حل مبسط جداً لإنشاء الطلاب

-- 1. إنشاء دالة مبسطة جداً
CREATE OR REPLACE FUNCTION create_student_ultra_simple(
  p_name text,
  p_grade text,
  p_tenant_id uuid
)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  new_student_id uuid;
  student_number text;
  result json;
BEGIN
  -- Generate new UUID
  new_student_id := gen_random_uuid();
  
  -- Generate student number
  student_number := 'STU-' || EXTRACT(YEAR FROM NOW()) || '-' || LPAD((EXTRACT(EPOCH FROM NOW())::bigint % 10000)::text, 4, '0');
  
  -- Insert student
  INSERT INTO students (
    id,
    name,
    grade,
    tenant_id,
    student_id,
    is_active,
    created_at,
    updated_at
  ) VALUES (
    new_student_id,
    p_name,
    p_grade,
    p_tenant_id,
    student_number,
    true,
    NOW(),
    NOW()
  );

  -- Return success
  result := json_build_object(
    'success', true,
    'student_id', new_student_id,
    'student_number', student_number,
    'message', 'تم إنشاء الطالب بنجاح'
  );

  RETURN result;

EXCEPTION
  WHEN OTHERS THEN
    result := json_build_object(
      'success', false,
      'error', SQLERRM,
      'message', 'فشل في إنشاء الطالب: ' || SQLERRM
    );
    RETURN result;
END;
$$;

-- منح الصلاحيات
GRANT EXECUTE ON FUNCTION create_student_ultra_simple(text, text, uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION create_student_ultra_simple(text, text, uuid) TO anon;

-- 2. دالة لتحديث معلومات الطالب الإضافية
CREATE OR REPLACE FUNCTION update_student_details(
  p_student_id uuid,
  p_parent_id uuid DEFAULT NULL,
  p_route_stop_id uuid DEFAULT NULL,
  p_email text DEFAULT NULL
)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result json;
BEGIN
  -- Update student
  UPDATE students 
  SET 
    parent_id = COALESCE(p_parent_id, parent_id),
    route_stop_id = COALESCE(p_route_stop_id, route_stop_id),
    updated_at = NOW()
  WHERE id = p_student_id;

  IF FOUND THEN
    result := json_build_object(
      'success', true,
      'message', 'تم تحديث معلومات الطالب'
    );
  ELSE
    result := json_build_object(
      'success', false,
      'error', 'الطالب غير موجود'
    );
  END IF;

  RETURN result;

EXCEPTION
  WHEN OTHERS THEN
    result := json_build_object(
      'success', false,
      'error', SQLERRM
    );
    RETURN result;
END;
$$;

-- منح الصلاحيات
GRANT EXECUTE ON FUNCTION update_student_details(uuid, uuid, uuid, text) TO authenticated;

-- 3. التحقق من نجاح التطبيق
SELECT 
  'create_student_ultra_simple' as function_name,
  CASE WHEN EXISTS (
    SELECT 1 FROM information_schema.routines 
    WHERE routine_name = 'create_student_ultra_simple'
  ) THEN 'موجودة ✅' ELSE 'غير موجودة ❌' END as status
UNION ALL
SELECT 
  'update_student_details' as function_name,
  CASE WHEN EXISTS (
    SELECT 1 FROM information_schema.routines 
    WHERE routine_name = 'update_student_details'
  ) THEN 'موجودة ✅' ELSE 'غير موجودة ❌' END as status;
