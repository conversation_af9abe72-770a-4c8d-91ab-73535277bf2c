-- ===================================================================
-- المرحلة الأولى: بناء نظام الصلاحيات المركزي
-- Phase 1: Centralized Permission System Implementation
-- Generated: 2025-01-30
-- ===================================================================

-- ===== 1. إنشاء الدوال المساعدة الموحدة والآمنة =====
-- Create unified and secure helper functions

-- دالة موحدة للتحقق من كون المستخدم أدمن
CREATE OR REPLACE FUNCTION public.is_system_admin(user_id uuid DEFAULT auth.uid())
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  user_role text;
BEGIN
  -- استعلام مباشر بدون RLS لتجنب التكرار اللا نهائي
  SELECT role INTO user_role 
  FROM public.users 
  WHERE id = COALESCE(user_id, auth.uid());
  
  RETURN COALESCE(user_role = 'admin', false);
EXCEPTION
  WHEN OTHERS THEN
    RETURN false;
END;
$$;

-- دالة موحدة للحصول على دور المستخدم
CREATE OR REPLACE FUNCTION public.get_user_role_secure(user_id uuid DEFAULT auth.uid())
RETURNS text
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  user_role text;
BEGIN
  -- استعلام مباشر بدون RLS
  SELECT role INTO user_role 
  FROM public.users 
  WHERE id = COALESCE(user_id, auth.uid());
  
  RETURN COALESCE(user_role, 'student');
EXCEPTION
  WHEN OTHERS THEN
    RETURN 'student';
END;
$$;

-- دالة موحدة للحصول على معرف المستأجر
CREATE OR REPLACE FUNCTION public.get_user_tenant_secure(user_id uuid DEFAULT auth.uid())
RETURNS uuid
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  user_tenant_id uuid;
BEGIN
  -- استعلام مباشر بدون RLS
  SELECT tenant_id INTO user_tenant_id 
  FROM public.users 
  WHERE id = COALESCE(user_id, auth.uid());
  
  RETURN user_tenant_id;
EXCEPTION
  WHEN OTHERS THEN
    RETURN null;
END;
$$;

-- دالة للتحقق من صلاحية الوصول للمستأجر
CREATE OR REPLACE FUNCTION public.can_access_tenant(target_tenant_id uuid, user_id uuid DEFAULT auth.uid())
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- الأدمن يمكنه الوصول لجميع المستأجرين
  IF public.is_system_admin(user_id) THEN
    RETURN true;
  END IF;
  
  -- المستخدمون الآخرون يمكنهم الوصول لمستأجرهم فقط
  RETURN public.get_user_tenant_secure(user_id) = target_tenant_id;
EXCEPTION
  WHEN OTHERS THEN
    RETURN false;
END;
$$;

-- دالة للتحقق من صلاحية إدارة المستخدمين
CREATE OR REPLACE FUNCTION public.can_manage_users(target_tenant_id uuid, user_id uuid DEFAULT auth.uid())
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  user_role text;
  user_tenant_id uuid;
BEGIN
  -- الحصول على معلومات المستخدم
  SELECT role, tenant_id INTO user_role, user_tenant_id
  FROM public.users 
  WHERE id = COALESCE(user_id, auth.uid());
  
  -- الأدمن يمكنه إدارة جميع المستخدمين
  IF user_role = 'admin' THEN
    RETURN true;
  END IF;
  
  -- مدير المدرسة يمكنه إدارة مستخدمي مدرسته فقط
  IF user_role = 'school_manager' AND user_tenant_id = target_tenant_id THEN
    RETURN true;
  END IF;
  
  RETURN false;
EXCEPTION
  WHEN OTHERS THEN
    RETURN false;
END;
$$;

-- دالة لتسجيل الأحداث الأمنية
CREATE OR REPLACE FUNCTION public.log_security_event(
  event_type text,
  severity text,
  description text,
  user_id uuid DEFAULT auth.uid(),
  tenant_id uuid DEFAULT null,
  metadata jsonb DEFAULT '{}'::jsonb
)
RETURNS uuid
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  event_id uuid;
BEGIN
  INSERT INTO public.security_events (
    user_id,
    tenant_id,
    event_type,
    severity,
    description,
    metadata,
    ip_address,
    user_agent,
    created_at
  ) VALUES (
    COALESCE(user_id, auth.uid()),
    COALESCE(tenant_id, public.get_user_tenant_secure()),
    event_type,
    severity,
    description,
    metadata,
    COALESCE(current_setting('request.headers', true)::json->>'x-forwarded-for', 'unknown'),
    COALESCE(current_setting('request.headers', true)::json->>'user-agent', 'unknown'),
    now()
  ) RETURNING id INTO event_id;
  
  RETURN event_id;
EXCEPTION
  WHEN OTHERS THEN
    -- في حالة فشل التسجيل، لا نريد أن نوقف العملية
    RETURN null;
END;
$$;

-- ===== 2. منح الصلاحيات للدوال الجديدة =====
-- Grant permissions to new functions

GRANT EXECUTE ON FUNCTION public.is_system_admin(uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_role_secure(uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_tenant_secure(uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION public.can_access_tenant(uuid, uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION public.can_manage_users(uuid, uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION public.log_security_event(text, text, text, uuid, uuid, jsonb) TO authenticated;

-- ===== 3. إنشاء جدول مصفوفة الصلاحيات =====
-- Create permission matrix table

CREATE TABLE IF NOT EXISTS public.permission_matrix (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  role text NOT NULL,
  resource_type text NOT NULL,
  action text NOT NULL,
  scope text NOT NULL DEFAULT 'tenant', -- 'global', 'tenant', 'own'
  conditions jsonb DEFAULT '{}'::jsonb,
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  
  UNIQUE(role, resource_type, action, scope)
);

-- إدراج مصفوفة الصلاحيات الأساسية
INSERT INTO public.permission_matrix (role, resource_type, action, scope, conditions) VALUES
-- صلاحيات الأدمن العام
('admin', 'user', 'create', 'global', '{}'),
('admin', 'user', 'read', 'global', '{}'),
('admin', 'user', 'update', 'global', '{}'),
('admin', 'user', 'delete', 'global', '{}'),
('admin', 'tenant', 'create', 'global', '{}'),
('admin', 'tenant', 'read', 'global', '{}'),
('admin', 'tenant', 'update', 'global', '{}'),
('admin', 'tenant', 'delete', 'global', '{}'),
('admin', 'bus', 'create', 'global', '{}'),
('admin', 'bus', 'read', 'global', '{}'),
('admin', 'bus', 'update', 'global', '{}'),
('admin', 'bus', 'delete', 'global', '{}'),
('admin', 'route', 'create', 'global', '{}'),
('admin', 'route', 'read', 'global', '{}'),
('admin', 'route', 'update', 'global', '{}'),
('admin', 'route', 'delete', 'global', '{}'),
('admin', 'student', 'create', 'global', '{}'),
('admin', 'student', 'read', 'global', '{}'),
('admin', 'student', 'update', 'global', '{}'),
('admin', 'student', 'delete', 'global', '{}'),
('admin', 'attendance', 'create', 'global', '{}'),
('admin', 'attendance', 'read', 'global', '{}'),
('admin', 'attendance', 'update', 'global', '{}'),
('admin', 'attendance', 'delete', 'global', '{}'),

-- صلاحيات مدير المدرسة
('school_manager', 'user', 'create', 'tenant', '{"exclude_roles": ["admin"]}'),
('school_manager', 'user', 'read', 'tenant', '{}'),
('school_manager', 'user', 'update', 'tenant', '{"exclude_roles": ["admin"]}'),
('school_manager', 'user', 'delete', 'tenant', '{"exclude_roles": ["admin"]}'),
('school_manager', 'tenant', 'read', 'own', '{}'),
('school_manager', 'tenant', 'update', 'own', '{}'),
('school_manager', 'bus', 'create', 'tenant', '{}'),
('school_manager', 'bus', 'read', 'tenant', '{}'),
('school_manager', 'bus', 'update', 'tenant', '{}'),
('school_manager', 'bus', 'delete', 'tenant', '{}'),
('school_manager', 'route', 'create', 'tenant', '{}'),
('school_manager', 'route', 'read', 'tenant', '{}'),
('school_manager', 'route', 'update', 'tenant', '{}'),
('school_manager', 'route', 'delete', 'tenant', '{}'),
('school_manager', 'student', 'create', 'tenant', '{}'),
('school_manager', 'student', 'read', 'tenant', '{}'),
('school_manager', 'student', 'update', 'tenant', '{}'),
('school_manager', 'student', 'delete', 'tenant', '{}'),
('school_manager', 'attendance', 'create', 'tenant', '{}'),
('school_manager', 'attendance', 'read', 'tenant', '{}'),
('school_manager', 'attendance', 'update', 'tenant', '{}'),
('school_manager', 'attendance', 'delete', 'tenant', '{}'),

-- صلاحيات المشرف
('supervisor', 'user', 'read', 'tenant', '{}'),
('supervisor', 'bus', 'read', 'tenant', '{}'),
('supervisor', 'bus', 'update', 'tenant', '{}'),
('supervisor', 'route', 'read', 'tenant', '{}'),
('supervisor', 'route', 'update', 'tenant', '{}'),
('supervisor', 'student', 'read', 'tenant', '{}'),
('supervisor', 'student', 'update', 'tenant', '{}'),
('supervisor', 'attendance', 'create', 'tenant', '{}'),
('supervisor', 'attendance', 'read', 'tenant', '{}'),
('supervisor', 'attendance', 'update', 'tenant', '{}'),

-- صلاحيات السائق
('driver', 'user', 'read', 'own', '{}'),
('driver', 'user', 'update', 'own', '{}'),
('driver', 'bus', 'read', 'assigned', '{}'),
('driver', 'bus', 'update', 'assigned', '{}'),
('driver', 'route', 'read', 'assigned', '{}'),
('driver', 'student', 'read', 'route', '{}'),
('driver', 'attendance', 'create', 'route', '{}'),
('driver', 'attendance', 'read', 'route', '{}'),

-- صلاحيات ولي الأمر
('parent', 'user', 'read', 'own', '{}'),
('parent', 'user', 'update', 'own', '{}'),
('parent', 'student', 'read', 'children', '{}'),
('parent', 'student', 'update', 'children', '{}'),
('parent', 'attendance', 'read', 'children', '{}'),
('parent', 'bus', 'read', 'children', '{}'),
('parent', 'route', 'read', 'children', '{}'),

-- صلاحيات الطالب
('student', 'user', 'read', 'own', '{}'),
('student', 'user', 'update', 'own', '{}'),
('student', 'student', 'read', 'own', '{}'),
('student', 'attendance', 'read', 'own', '{}'),
('student', 'bus', 'read', 'own', '{}'),
('student', 'route', 'read', 'own', '{}');

-- إنشاء فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_permission_matrix_role_resource
ON public.permission_matrix(role, resource_type);

CREATE INDEX IF NOT EXISTS idx_permission_matrix_active
ON public.permission_matrix(is_active) WHERE is_active = true;

-- تفعيل RLS على جدول مصفوفة الصلاحيات
ALTER TABLE public.permission_matrix ENABLE ROW LEVEL SECURITY;

-- سياسة للوصول لمصفوفة الصلاحيات (قراءة فقط للجميع، تعديل للأدمن فقط)
CREATE POLICY "permission_matrix_read_access" ON public.permission_matrix
FOR SELECT TO authenticated
USING (true);

CREATE POLICY "permission_matrix_admin_manage" ON public.permission_matrix
FOR ALL TO authenticated
USING (public.is_system_admin())
WITH CHECK (public.is_system_admin());

-- تسجيل إتمام بناء النظام المركزي
SELECT public.log_security_event(
  'CENTRALIZED_SYSTEM_CREATED',
  'INFO',
  'Phase 1 centralized permission system implemented successfully',
  auth.uid(),
  null,
  jsonb_build_object(
    'phase', 1,
    'action', 'centralized_system',
    'functions_created', 6,
    'permission_matrix_entries', 78
  )
);
