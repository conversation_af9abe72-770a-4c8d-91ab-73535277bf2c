-- ===================================================================
-- إصلاحات شاملة لقاعدة البيانات - Comprehensive Database Fixes
-- تاريخ الإنشاء: 2025-01-01
-- الوصف: يحل جميع المشاكل المكتشفة في قاعدة البيانات
-- ===================================================================

-- 1. إضافة الأعمدة المفقودة
-- ===================================================================

-- إضافة عمود notes للحافلات
ALTER TABLE buses ADD COLUMN IF NOT EXISTS notes TEXT;

-- إضافة عمود scheduled_date لصيانة الحافلات (إذا لم يكن موجوداً)
ALTER TABLE bus_maintenance ADD COLUMN IF NOT EXISTS scheduled_date DATE;

-- إضافة عمود priority لصيانة الحافلات
ALTER TABLE bus_maintenance ADD COLUMN IF NOT EXISTS priority VARCHAR(20) DEFAULT 'normal' 
CHECK (priority IN ('low', 'normal', 'high', 'urgent'));

-- 2. إنشاء جدول تاريخ الصيانة
-- ===================================================================

CREATE TABLE IF NOT EXISTS maintenance_history (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  bus_id UUID REFERENCES buses(id) ON DELETE CASCADE,
  maintenance_date DATE NOT NULL,
  maintenance_type VARCHAR(50) NOT NULL,
  description TEXT,
  cost DECIMAL(10,2),
  mechanic_name VARCHAR(100),
  parts_used JSONB,
  before_photos TEXT[],
  after_photos TEXT[],
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  tenant_id UUID REFERENCES tenants(id) NOT NULL,
  created_by UUID REFERENCES users(id)
);

-- 3. إنشاء جدول سجل الأمان
-- ===================================================================

CREATE TABLE IF NOT EXISTS security_audit_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id),
  user_role VARCHAR(50),
  resource_type VARCHAR(50),
  action VARCHAR(50),
  allowed BOOLEAN,
  reason TEXT,
  ip_address INET,
  user_agent TEXT,
  timestamp TIMESTAMP DEFAULT NOW(),
  tenant_id UUID REFERENCES tenants(id)
);

-- 4. إنشاء جدول تحليلات الإشعارات
-- ===================================================================

CREATE TABLE IF NOT EXISTS notification_analytics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  notification_id UUID,
  event_type VARCHAR(50) NOT NULL, -- 'sent', 'delivered', 'clicked', 'failed'
  notification_data JSONB,
  tenant_id UUID REFERENCES tenants(id),
  user_id UUID REFERENCES users(id),
  timestamp TIMESTAMP DEFAULT NOW(),
  metadata JSONB
);

-- 5. إضافة القيود والفهارس
-- ===================================================================

-- قيود unique
ALTER TABLE students 
ADD CONSTRAINT IF NOT EXISTS unique_student_id_per_tenant 
UNIQUE (student_id, tenant_id);

-- قيود check
ALTER TABLE buses 
ADD CONSTRAINT IF NOT EXISTS check_capacity 
CHECK (capacity > 0 AND capacity <= 100);

ALTER TABLE buses 
ADD CONSTRAINT IF NOT EXISTS check_plate_number_format 
CHECK (plate_number ~ '^[A-Z]{1,3}-[0-9]{1,4}$');

-- فهارس محسنة للأداء
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_students_tenant_grade 
ON students(tenant_id, grade) WHERE is_active = true;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_students_parent_id 
ON students(parent_id) WHERE parent_id IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_attendance_date_student 
ON attendance(recorded_at, student_id, tenant_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_buses_tenant_active 
ON buses(tenant_id, is_active) WHERE is_active = true;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_buses_driver_id 
ON buses(driver_id) WHERE driver_id IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_notifications_user_unread 
ON notifications(user_id, created_at) WHERE read = false;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_maintenance_history_bus_date 
ON maintenance_history(bus_id, maintenance_date DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_security_audit_user_time 
ON security_audit_logs(user_id, timestamp DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_notification_analytics_tenant_time 
ON notification_analytics(tenant_id, timestamp DESC);

-- 6. تفعيل RLS على الجداول الجديدة
-- ===================================================================

ALTER TABLE maintenance_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE security_audit_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE notification_analytics ENABLE ROW LEVEL SECURITY;

-- 7. إنشاء سياسات RLS محسنة
-- ===================================================================

-- سياسة تاريخ الصيانة
CREATE POLICY "Users can access maintenance history"
ON maintenance_history
FOR ALL
TO authenticated
USING (
  public.is_admin() OR
  tenant_id = public.get_user_tenant_id()
);

-- سياسة سجل الأمان (للمديرين فقط)
CREATE POLICY "Admins can access security audit logs"
ON security_audit_logs
FOR ALL
TO authenticated
USING (public.is_admin());

-- سياسة تحليلات الإشعارات
CREATE POLICY "Users can access notification analytics"
ON notification_analytics
FOR ALL
TO authenticated
USING (
  public.is_admin() OR
  tenant_id = public.get_user_tenant_id()
);

-- تحسين سياسة الحضور
DROP POLICY IF EXISTS "Users can access attendance" ON public.attendance;

CREATE POLICY "Users can access attendance optimized"
ON public.attendance
FOR ALL
TO authenticated
USING (
  -- Admin access
  public.is_admin() OR
  -- Direct user access (for drivers recording attendance)
  recorded_by = auth.uid() OR
  -- Parent access (optimized with index)
  EXISTS (
    SELECT 1 FROM public.students s 
    WHERE s.id = attendance.student_id 
    AND s.parent_id = auth.uid()
  ) OR
  -- Tenant access (optimized with index)
  tenant_id = public.get_user_tenant_id()
);

-- 8. إنشاء دوال التحقق من القيود
-- ===================================================================

-- دالة للتحقق من تكرار رقم الطالب
CREATE OR REPLACE FUNCTION check_unique_student_id(
  p_student_id VARCHAR,
  p_tenant_id UUID,
  p_exclude_id UUID DEFAULT NULL
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN NOT EXISTS (
    SELECT 1 FROM students 
    WHERE student_id = p_student_id 
    AND tenant_id = p_tenant_id 
    AND is_active = true
    AND (p_exclude_id IS NULL OR id != p_exclude_id)
  );
END;
$$;

-- دالة للتحقق من سعة الحافلة
CREATE OR REPLACE FUNCTION check_bus_capacity(
  p_bus_id UUID,
  p_additional_students INTEGER DEFAULT 1
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  bus_capacity INTEGER;
  current_students INTEGER;
BEGIN
  -- الحصول على سعة الحافلة
  SELECT capacity INTO bus_capacity
  FROM buses WHERE id = p_bus_id AND is_active = true;
  
  IF bus_capacity IS NULL THEN
    RETURN FALSE;
  END IF;
  
  -- حساب عدد الطلاب الحاليين
  SELECT COUNT(*) INTO current_students
  FROM students s
  JOIN route_stops rs ON s.route_stop_id = rs.id
  JOIN routes r ON rs.route_id = r.id
  WHERE r.bus_id = p_bus_id AND s.is_active = true;
  
  -- التحقق من السعة
  RETURN (current_students + p_additional_students) <= bus_capacity;
END;
$$;

-- دالة للتحقق من تعيين السائق
CREATE OR REPLACE FUNCTION check_driver_assignment(
  p_driver_id UUID,
  p_exclude_bus_id UUID DEFAULT NULL
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN NOT EXISTS (
    SELECT 1 FROM buses 
    WHERE driver_id = p_driver_id 
    AND is_active = true
    AND (p_exclude_bus_id IS NULL OR id != p_exclude_bus_id)
  );
END;
$$;

-- دالة للتحقق من علاقة ولي الأمر بالطالب
CREATE OR REPLACE FUNCTION check_parent_student_relation(
  p_parent_id UUID,
  p_student_id UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM students 
    WHERE id = p_student_id 
    AND parent_id = p_parent_id
    AND is_active = true
  );
END;
$$;

-- دالة للحصول على إحصائيات الحافلة
CREATE OR REPLACE FUNCTION get_bus_statistics(p_bus_id UUID)
RETURNS TABLE (
  total_capacity INTEGER,
  current_students INTEGER,
  available_seats INTEGER,
  utilization_percentage DECIMAL
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  bus_capacity INTEGER;
  student_count INTEGER;
BEGIN
  -- الحصول على سعة الحافلة
  SELECT capacity INTO bus_capacity
  FROM buses WHERE id = p_bus_id AND is_active = true;
  
  IF bus_capacity IS NULL THEN
    RETURN;
  END IF;
  
  -- حساب عدد الطلاب الحاليين
  SELECT COUNT(*) INTO student_count
  FROM students s
  JOIN route_stops rs ON s.route_stop_id = rs.id
  JOIN routes r ON rs.route_id = r.id
  WHERE r.bus_id = p_bus_id AND s.is_active = true;
  
  -- إرجاع الإحصائيات
  total_capacity := bus_capacity;
  current_students := student_count;
  available_seats := bus_capacity - student_count;
  utilization_percentage := ROUND((student_count::DECIMAL / bus_capacity::DECIMAL) * 100, 2);
  
  RETURN NEXT;
END;
$$;

-- 9. منح الصلاحيات
-- ===================================================================

GRANT EXECUTE ON FUNCTION check_unique_student_id(VARCHAR, UUID, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION check_bus_capacity(UUID, INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION check_driver_assignment(UUID, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION check_parent_student_relation(UUID, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_bus_statistics(UUID) TO authenticated;

-- 10. إنشاء triggers للتحديث التلقائي
-- ===================================================================

-- trigger لتحديث updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- تطبيق trigger على الجداول
CREATE TRIGGER update_maintenance_history_updated_at 
  BEFORE UPDATE ON maintenance_history 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 11. إضافة تعليقات للتوثيق
-- ===================================================================

COMMENT ON TABLE maintenance_history IS 'تاريخ صيانة الحافلات مع التفاصيل والتكاليف';
COMMENT ON TABLE security_audit_logs IS 'سجل مراجعة الأمان لتتبع محاولات الوصول';
COMMENT ON TABLE notification_analytics IS 'تحليلات الإشعارات لقياس الفعالية';

COMMENT ON FUNCTION check_unique_student_id IS 'التحقق من عدم تكرار رقم الطالب في المدرسة';
COMMENT ON FUNCTION check_bus_capacity IS 'التحقق من سعة الحافلة قبل إضافة طلاب';
COMMENT ON FUNCTION check_driver_assignment IS 'التحقق من عدم تعيين السائق لحافلة أخرى';
COMMENT ON FUNCTION check_parent_student_relation IS 'التحقق من علاقة ولي الأمر بالطالب';

-- 12. إنشاء views مفيدة
-- ===================================================================

-- view لإحصائيات الحافلات
CREATE OR REPLACE VIEW bus_utilization_stats AS
SELECT 
  b.id,
  b.plate_number,
  b.capacity,
  COUNT(s.id) as current_students,
  (b.capacity - COUNT(s.id)) as available_seats,
  ROUND((COUNT(s.id)::DECIMAL / b.capacity::DECIMAL) * 100, 2) as utilization_percentage,
  b.tenant_id
FROM buses b
LEFT JOIN routes r ON b.id = r.bus_id
LEFT JOIN route_stops rs ON r.id = rs.route_id
LEFT JOIN students s ON rs.id = s.route_stop_id AND s.is_active = true
WHERE b.is_active = true
GROUP BY b.id, b.plate_number, b.capacity, b.tenant_id;

-- view لإحصائيات الحضور
CREATE OR REPLACE VIEW attendance_summary AS
SELECT 
  s.id as student_id,
  s.name as student_name,
  s.grade,
  COUNT(a.id) as total_records,
  COUNT(CASE WHEN a.status = 'present' THEN 1 END) as present_count,
  COUNT(CASE WHEN a.status = 'absent' THEN 1 END) as absent_count,
  ROUND(
    (COUNT(CASE WHEN a.status = 'present' THEN 1 END)::DECIMAL / 
     NULLIF(COUNT(a.id), 0)::DECIMAL) * 100, 2
  ) as attendance_rate,
  s.tenant_id
FROM students s
LEFT JOIN attendance a ON s.id = a.student_id
WHERE s.is_active = true
GROUP BY s.id, s.name, s.grade, s.tenant_id;

-- منح صلاحيات على Views
GRANT SELECT ON bus_utilization_stats TO authenticated;
GRANT SELECT ON attendance_summary TO authenticated;

-- تفعيل RLS على Views
ALTER VIEW bus_utilization_stats SET (security_invoker = true);
ALTER VIEW attendance_summary SET (security_invoker = true);
