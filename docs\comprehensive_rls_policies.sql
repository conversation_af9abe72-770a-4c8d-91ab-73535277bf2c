-- إصلاح شامل لسياسات RLS مع دعم CRUD كامل
-- Comprehensive RLS Policy Fix with Full CRUD Support

-- ===== 1. تنظيف السياسات الموجودة =====
-- Clean existing policies

-- إزالة جميع السياسات من جميع الجداول
DROP POLICY IF EXISTS "admin_full_access" ON users;
DROP POLICY IF EXISTS "school_manager_tenant_access" ON users;
DROP POLICY IF EXISTS "own_profile_access" ON users;
DROP POLICY IF EXISTS "tenant_users_view" ON users;
DROP POLICY IF EXISTS "users_own_profile" ON users;
DROP POLICY IF EXISTS "admin_access_all" ON users;
DROP POLICY IF EXISTS "school_manager_tenant" ON users;

DROP POLICY IF EXISTS "admin_full_access" ON students;
DROP POLICY IF EXISTS "school_staff_tenant_access" ON students;
DROP POLICY IF EXISTS "parent_children_access" ON students;
DROP POLICY IF EXISTS "student_own_profile" ON students;
DROP POLICY IF EXISTS "driver_tenant_view" ON students;

DROP POLICY IF EXISTS "admin_full_access" ON buses;
DROP POLICY IF EXISTS "school_staff_tenant_access" ON buses;
DROP POLICY IF EXISTS "driver_assigned_bus" ON buses;
DROP POLICY IF EXISTS "tenant_buses_view" ON buses;

DROP POLICY IF EXISTS "admin_full_access" ON routes;
DROP POLICY IF EXISTS "school_staff_tenant_access" ON routes;
DROP POLICY IF EXISTS "tenant_routes_view" ON routes;

DROP POLICY IF EXISTS "admin_full_access" ON tenants;
DROP POLICY IF EXISTS "school_manager_own_tenant" ON tenants;
DROP POLICY IF EXISTS "users_own_tenant_view" ON tenants;

DROP POLICY IF EXISTS "admin_full_access" ON attendance;
DROP POLICY IF EXISTS "tenant_staff_access" ON attendance;
DROP POLICY IF EXISTS "parent_children_attendance" ON attendance;

-- ===== 2. إنشاء دوال مساعدة محسنة =====
-- Create enhanced helper functions

-- دالة للتحقق من كون المستخدم أدمن
CREATE OR REPLACE FUNCTION auth_is_admin()
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
STABLE
AS $$
BEGIN
  RETURN COALESCE(
    (SELECT role = 'admin' FROM users WHERE id = auth.uid()),
    false
  );
EXCEPTION
  WHEN OTHERS THEN
    RETURN false;
END;
$$;

-- دالة للحصول على tenant_id للمستخدم الحالي
CREATE OR REPLACE FUNCTION auth_user_tenant_id()
RETURNS uuid
LANGUAGE plpgsql
SECURITY DEFINER
STABLE
AS $$
BEGIN
  RETURN (SELECT tenant_id FROM users WHERE id = auth.uid());
EXCEPTION
  WHEN OTHERS THEN
    RETURN null;
END;
$$;

-- دالة للحصول على دور المستخدم الحالي
CREATE OR REPLACE FUNCTION auth_user_role()
RETURNS text
LANGUAGE plpgsql
SECURITY DEFINER
STABLE
AS $$
BEGIN
  RETURN COALESCE(
    (SELECT role::text FROM users WHERE id = auth.uid()),
    'student'
  );
EXCEPTION
  WHEN OTHERS THEN
    RETURN 'student';
END;
$$;

-- دالة للتحقق من صلاحية إدارة المستخدمين
CREATE OR REPLACE FUNCTION can_manage_users(target_tenant_id uuid DEFAULT NULL)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
STABLE
AS $$
DECLARE
  user_role text;
  user_tenant uuid;
BEGIN
  SELECT role::text, tenant_id INTO user_role, user_tenant 
  FROM users WHERE id = auth.uid();
  
  -- الأدمن يمكنه إدارة جميع المستخدمين
  IF user_role = 'admin' THEN
    RETURN true;
  END IF;
  
  -- مدير المدرسة يمكنه إدارة مستخدمي مدرسته فقط
  IF user_role = 'school_manager' AND user_tenant = target_tenant_id THEN
    RETURN true;
  END IF;
  
  RETURN false;
EXCEPTION
  WHEN OTHERS THEN
    RETURN false;
END;
$$;

-- منح الصلاحيات للدوال
GRANT EXECUTE ON FUNCTION auth_is_admin() TO authenticated;
GRANT EXECUTE ON FUNCTION auth_user_tenant_id() TO authenticated;
GRANT EXECUTE ON FUNCTION auth_user_role() TO authenticated;
GRANT EXECUTE ON FUNCTION can_manage_users(uuid) TO authenticated;

-- ===== 3. تفعيل RLS وإنشاء سياسات المستخدمين =====
-- Enable RLS and create user policies

ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- سياسة 1: الأدمن يمكنه الوصول لجميع المستخدمين
CREATE POLICY "admin_users_full_access" ON users
  FOR ALL
  TO authenticated
  USING (auth_is_admin())
  WITH CHECK (auth_is_admin());

-- سياسة 2: المستخدمون يمكنهم الوصول لملفهم الشخصي
CREATE POLICY "users_own_profile_access" ON users
  FOR ALL
  TO authenticated
  USING (id = auth.uid())
  WITH CHECK (id = auth.uid());

-- سياسة 3: مديرو المدارس يمكنهم إدارة مستخدمي مدرستهم
CREATE POLICY "school_manager_users_access" ON users
  FOR ALL
  TO authenticated
  USING (
    auth_user_role() = 'school_manager' AND
    tenant_id = auth_user_tenant_id() AND
    role != 'admin'
  )
  WITH CHECK (
    auth_user_role() = 'school_manager' AND
    tenant_id = auth_user_tenant_id() AND
    role != 'admin'
  );

-- سياسة 4: المشرفون يمكنهم عرض مستخدمي مدرستهم
CREATE POLICY "supervisor_users_view" ON users
  FOR SELECT
  TO authenticated
  USING (
    auth_user_role() IN ('supervisor', 'driver') AND
    tenant_id = auth_user_tenant_id()
  );

-- ===== 4. سياسات الطلاب =====
-- Student policies

ALTER TABLE students ENABLE ROW LEVEL SECURITY;

-- سياسة 1: الأدمن يمكنه الوصول لجميع الطلاب
CREATE POLICY "admin_students_full_access" ON students
  FOR ALL
  TO authenticated
  USING (auth_is_admin())
  WITH CHECK (auth_is_admin());

-- سياسة 2: موظفو المدرسة يمكنهم إدارة طلاب مدرستهم
CREATE POLICY "school_staff_students_access" ON students
  FOR ALL
  TO authenticated
  USING (
    auth_user_role() IN ('school_manager', 'supervisor') AND
    tenant_id = auth_user_tenant_id()
  )
  WITH CHECK (
    auth_user_role() IN ('school_manager', 'supervisor') AND
    tenant_id = auth_user_tenant_id()
  );

-- سياسة 3: الطلاب يمكنهم عرض ملفهم الشخصي
CREATE POLICY "students_own_profile_view" ON students
  FOR SELECT
  TO authenticated
  USING (id = auth.uid());

-- سياسة 4: أولياء الأمور يمكنهم عرض أطفالهم
CREATE POLICY "parents_children_view" ON students
  FOR SELECT
  TO authenticated
  USING (
    auth_user_role() = 'parent' AND
    parent_id = auth.uid()
  );

-- سياسة 5: السائقون يمكنهم عرض طلاب مساراتهم
CREATE POLICY "drivers_route_students_view" ON students
  FOR SELECT
  TO authenticated
  USING (
    auth_user_role() = 'driver' AND
    tenant_id = auth_user_tenant_id()
  );

-- ===== 5. سياسات الحافلات =====
-- Bus policies

ALTER TABLE buses ENABLE ROW LEVEL SECURITY;

-- سياسة 1: الأدمن يمكنه الوصول لجميع الحافلات
CREATE POLICY "admin_buses_full_access" ON buses
  FOR ALL
  TO authenticated
  USING (auth_is_admin())
  WITH CHECK (auth_is_admin());

-- سياسة 2: موظفو المدرسة يمكنهم إدارة حافلات مدرستهم
CREATE POLICY "school_staff_buses_access" ON buses
  FOR ALL
  TO authenticated
  USING (
    auth_user_role() IN ('school_manager', 'supervisor') AND
    tenant_id = auth_user_tenant_id()
  )
  WITH CHECK (
    auth_user_role() IN ('school_manager', 'supervisor') AND
    tenant_id = auth_user_tenant_id()
  );

-- سياسة 3: السائقون يمكنهم عرض وتحديث حافلاتهم المخصصة
CREATE POLICY "drivers_assigned_buses_access" ON buses
  FOR ALL
  TO authenticated
  USING (
    auth_user_role() = 'driver' AND
    (driver_id = auth.uid() OR tenant_id = auth_user_tenant_id())
  )
  WITH CHECK (
    auth_user_role() = 'driver' AND
    tenant_id = auth_user_tenant_id()
  );

-- سياسة 4: جميع مستخدمي المدرسة يمكنهم عرض حافلات مدرستهم
CREATE POLICY "tenant_buses_view_only" ON buses
  FOR SELECT
  TO authenticated
  USING (tenant_id = auth_user_tenant_id());

-- ===== 6. سياسات المسارات =====
-- Route policies

ALTER TABLE routes ENABLE ROW LEVEL SECURITY;

-- سياسة 1: الأدمن يمكنه الوصول لجميع المسارات
CREATE POLICY "admin_routes_full_access" ON routes
  FOR ALL
  TO authenticated
  USING (auth_is_admin())
  WITH CHECK (auth_is_admin());

-- سياسة 2: موظفو المدرسة يمكنهم إدارة مسارات مدرستهم
CREATE POLICY "school_staff_routes_access" ON routes
  FOR ALL
  TO authenticated
  USING (
    auth_user_role() IN ('school_manager', 'supervisor') AND
    tenant_id = auth_user_tenant_id()
  )
  WITH CHECK (
    auth_user_role() IN ('school_manager', 'supervisor') AND
    tenant_id = auth_user_tenant_id()
  );

-- سياسة 3: جميع مستخدمي المدرسة يمكنهم عرض مسارات مدرستهم
CREATE POLICY "tenant_routes_view_only" ON routes
  FOR SELECT
  TO authenticated
  USING (tenant_id = auth_user_tenant_id());

-- ===== 7. سياسات المدارس =====
-- Tenant policies

ALTER TABLE tenants ENABLE ROW LEVEL SECURITY;

-- سياسة 1: الأدمن يمكنه الوصول لجميع المدارس
CREATE POLICY "admin_tenants_full_access" ON tenants
  FOR ALL
  TO authenticated
  USING (auth_is_admin())
  WITH CHECK (auth_is_admin());

-- سياسة 2: مديرو المدارس يمكنهم عرض وتحديث مدرستهم
CREATE POLICY "school_manager_own_tenant_access" ON tenants
  FOR ALL
  TO authenticated
  USING (
    auth_user_role() = 'school_manager' AND
    id = auth_user_tenant_id()
  )
  WITH CHECK (
    auth_user_role() = 'school_manager' AND
    id = auth_user_tenant_id()
  );

-- سياسة 3: جميع المستخدمين يمكنهم عرض مدرستهم
CREATE POLICY "users_own_tenant_view_only" ON tenants
  FOR SELECT
  TO authenticated
  USING (id = auth_user_tenant_id());

-- ===== 8. سياسات الحضور =====
-- Attendance policies

ALTER TABLE attendance ENABLE ROW LEVEL SECURITY;

-- سياسة 1: الأدمن يمكنه الوصول لجميع سجلات الحضور
CREATE POLICY "admin_attendance_full_access" ON attendance
  FOR ALL
  TO authenticated
  USING (auth_is_admin())
  WITH CHECK (auth_is_admin());

-- سياسة 2: موظفو المدرسة يمكنهم إدارة حضور مدرستهم
CREATE POLICY "school_staff_attendance_access" ON attendance
  FOR ALL
  TO authenticated
  USING (
    auth_user_role() IN ('school_manager', 'supervisor', 'driver') AND
    tenant_id = auth_user_tenant_id()
  )
  WITH CHECK (
    auth_user_role() IN ('school_manager', 'supervisor', 'driver') AND
    tenant_id = auth_user_tenant_id()
  );

-- سياسة 3: أولياء الأمور يمكنهم عرض حضور أطفالهم
CREATE POLICY "parents_children_attendance_view" ON attendance
  FOR SELECT
  TO authenticated
  USING (
    auth_user_role() = 'parent' AND
    EXISTS (
      SELECT 1 FROM students s 
      WHERE s.id = attendance.student_id 
      AND s.parent_id = auth.uid()
    )
  );

-- رسالة تأكيد
SELECT 'RLS policies created successfully with full CRUD support' as status;
