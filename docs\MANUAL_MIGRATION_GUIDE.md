# دليل التطبيق اليدوي للتهجيرات - المرحلة الأولى
# Manual Migration Application Guide - Phase 1

**في حالة فشل السكريبت التلقائي، يمكنك تطبيق التهجيرات يدوياً**

---

## 🚀 **طرق التطبيق**

### **الطريقة الأولى: السكريبت المبسط**

```bash
# تشغيل السكريبت المبسط
node scripts/apply-phase1-simple.js
```

### **الطريقة الثانية: عبر Supabase Dashboard**

1. **افتح Supabase Dashboard**
   - اذهب إلى: https://supabase.com/dashboard
   - اختر مشروعك: `pcavtwqvgnkgybzfqeuz`

2. **اذهب إلى SQL Editor**
   - من القائمة الجانبية، اختر "SQL Editor"

3. **طبق التهجيرات بالترتيب التالي:**

---

## 📄 **التهجير الأول: تنظيف النظام**

**انسخ والصق الكود التالي في SQL Editor:**

```sql
-- ===================================================================
-- المرحلة الأولى: تنظيف نظام الأمان والصلاحيات
-- Phase 1: Security System Cleanup
-- ===================================================================

-- إزالة السياسات المتضاربة
DROP POLICY IF EXISTS "admin_full_access_users" ON public.users;
DROP POLICY IF EXISTS "admin_users_full_access" ON public.users;
DROP POLICY IF EXISTS "school_manager_users_access" ON public.users;
DROP POLICY IF EXISTS "supervisor_users_view" ON public.users;
DROP POLICY IF EXISTS "users_own_profile_access" ON public.users;

-- إزالة الدوال المتضاربة
DROP FUNCTION IF EXISTS public.auth_is_admin();
DROP FUNCTION IF EXISTS public.is_admin();
DROP FUNCTION IF EXISTS public.is_admin_user();
DROP FUNCTION IF EXISTS public.auth_user_role();
DROP FUNCTION IF EXISTS public.get_current_user_role();

-- تعطيل RLS مؤقتاً
ALTER TABLE public.users DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.tenants DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.buses DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.routes DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.students DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.attendance DISABLE ROW LEVEL SECURITY;
```

**اضغط "Run" وتأكد من عدم وجود أخطاء**

---

## 📄 **التهجير الثاني: النظام المركزي**

```sql
-- ===================================================================
-- إنشاء الدوال الأساسية الجديدة
-- ===================================================================

-- دالة التحقق من الأدمن
CREATE OR REPLACE FUNCTION public.is_system_admin(user_id uuid DEFAULT auth.uid())
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  user_role text;
BEGIN
  SELECT role INTO user_role 
  FROM public.users 
  WHERE id = COALESCE(user_id, auth.uid());
  
  RETURN COALESCE(user_role = 'admin', false);
EXCEPTION
  WHEN OTHERS THEN
    RETURN false;
END;
$$;

-- دالة الحصول على الدور
CREATE OR REPLACE FUNCTION public.get_user_role_secure(user_id uuid DEFAULT auth.uid())
RETURNS text
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  user_role text;
BEGIN
  SELECT role INTO user_role 
  FROM public.users 
  WHERE id = COALESCE(user_id, auth.uid());
  
  RETURN COALESCE(user_role, 'student');
EXCEPTION
  WHEN OTHERS THEN
    RETURN 'student';
END;
$$;

-- دالة الحصول على المستأجر
CREATE OR REPLACE FUNCTION public.get_user_tenant_secure(user_id uuid DEFAULT auth.uid())
RETURNS uuid
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  user_tenant_id uuid;
BEGIN
  SELECT tenant_id INTO user_tenant_id 
  FROM public.users 
  WHERE id = COALESCE(user_id, auth.uid());
  
  RETURN user_tenant_id;
EXCEPTION
  WHEN OTHERS THEN
    RETURN null;
END;
$$;

-- منح الصلاحيات
GRANT EXECUTE ON FUNCTION public.is_system_admin(uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_role_secure(uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_tenant_secure(uuid) TO authenticated;
```

---

## 📄 **التهجير الثالث: مصفوفة الصلاحيات**

```sql
-- ===================================================================
-- إنشاء جدول مصفوفة الصلاحيات
-- ===================================================================

CREATE TABLE IF NOT EXISTS public.permission_matrix (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  role text NOT NULL,
  resource_type text NOT NULL,
  action text NOT NULL,
  scope text NOT NULL DEFAULT 'tenant',
  conditions jsonb DEFAULT '{}'::jsonb,
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  
  UNIQUE(role, resource_type, action, scope)
);

-- إدراج الصلاحيات الأساسية
INSERT INTO public.permission_matrix (role, resource_type, action, scope, conditions) VALUES
-- صلاحيات الأدمن
('admin', 'user', 'create', 'global', '{}'),
('admin', 'user', 'read', 'global', '{}'),
('admin', 'user', 'update', 'global', '{}'),
('admin', 'user', 'delete', 'global', '{}'),
('admin', 'tenant', 'create', 'global', '{}'),
('admin', 'tenant', 'read', 'global', '{}'),
('admin', 'tenant', 'update', 'global', '{}'),
('admin', 'tenant', 'delete', 'global', '{}'),

-- صلاحيات مدير المدرسة
('school_manager', 'user', 'create', 'tenant', '{"exclude_roles": ["admin"]}'),
('school_manager', 'user', 'read', 'tenant', '{}'),
('school_manager', 'user', 'update', 'tenant', '{"exclude_roles": ["admin"]}'),
('school_manager', 'tenant', 'read', 'own', '{}'),
('school_manager', 'tenant', 'update', 'own', '{}'),

-- صلاحيات ولي الأمر
('parent', 'user', 'read', 'own', '{}'),
('parent', 'user', 'update', 'own', '{}'),
('parent', 'student', 'read', 'children', '{}'),
('parent', 'attendance', 'read', 'children', '{}')

ON CONFLICT (role, resource_type, action, scope) DO NOTHING;

-- تفعيل RLS على مصفوفة الصلاحيات
ALTER TABLE public.permission_matrix ENABLE ROW LEVEL SECURITY;

CREATE POLICY "permission_matrix_read_access" ON public.permission_matrix
FOR SELECT TO authenticated
USING (true);
```

---

## 📄 **التهجير الرابع: دالة فحص الصلاحيات**

```sql
-- ===================================================================
-- دالة فحص الصلاحيات المركزية
-- ===================================================================

CREATE OR REPLACE FUNCTION public.check_permission(
  user_id uuid,
  resource_type text,
  action text,
  resource_tenant_id uuid DEFAULT null,
  resource_owner_id uuid DEFAULT null,
  additional_context jsonb DEFAULT '{}'::jsonb
)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  user_role text;
  user_tenant_id uuid;
  permission_scope text;
  has_permission boolean := false;
BEGIN
  -- الحصول على معلومات المستخدم
  user_role := public.get_user_role_secure(user_id);
  user_tenant_id := public.get_user_tenant_secure(user_id);
  
  -- البحث عن الصلاحية في المصفوفة
  SELECT scope INTO permission_scope
  FROM public.permission_matrix
  WHERE role = user_role 
    AND resource_type = check_permission.resource_type 
    AND action = check_permission.action 
    AND is_active = true
  LIMIT 1;
  
  -- إذا لم توجد صلاحية، الرفض
  IF permission_scope IS NULL THEN
    RETURN false;
  END IF;
  
  -- التحقق حسب نطاق الصلاحية
  CASE permission_scope
    WHEN 'global' THEN
      has_permission := true;
      
    WHEN 'tenant' THEN
      has_permission := (user_tenant_id = resource_tenant_id);
      
    WHEN 'own' THEN
      has_permission := (user_id = resource_owner_id);
      
    WHEN 'children' THEN
      -- لأولياء الأمور: التحقق من الأطفال
      IF user_role = 'parent' AND resource_type = 'student' THEN
        has_permission := EXISTS (
          SELECT 1 FROM public.students 
          WHERE id = (additional_context->>'resource_id')::uuid 
            AND parent_id = user_id
        );
      END IF;
      
    ELSE
      has_permission := false;
  END CASE;
  
  RETURN has_permission;
EXCEPTION
  WHEN OTHERS THEN
    RETURN false;
END;
$$;

-- منح الصلاحية للدالة
GRANT EXECUTE ON FUNCTION public.check_permission(uuid, text, text, uuid, uuid, jsonb) TO authenticated;
```

---

## 📄 **التهجير الخامس: السياسات الجديدة**

```sql
-- ===================================================================
-- تطبيق السياسات الموحدة الجديدة
-- ===================================================================

-- تفعيل RLS على الجداول
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tenants ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.buses ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.routes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.students ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.attendance ENABLE ROW LEVEL SECURITY;

-- سياسات جدول المستخدمين
CREATE POLICY "users_read_policy" ON public.users
FOR SELECT TO authenticated
USING (
  public.check_permission(
    auth.uid(),
    'user',
    'read',
    tenant_id,
    id,
    jsonb_build_object('resource_id', id)
  )
);

CREATE POLICY "users_update_policy" ON public.users
FOR UPDATE TO authenticated
USING (
  public.check_permission(
    auth.uid(),
    'user',
    'update',
    tenant_id,
    id,
    jsonb_build_object('resource_id', id)
  )
);

-- سياسات جدول الطلاب
CREATE POLICY "students_read_policy" ON public.students
FOR SELECT TO authenticated
USING (
  public.check_permission(
    auth.uid(),
    'student',
    'read',
    tenant_id,
    parent_id,
    jsonb_build_object('resource_id', id)
  )
);

-- سياسات جدول الحضور
CREATE POLICY "attendance_read_policy" ON public.attendance
FOR SELECT TO authenticated
USING (
  public.check_permission(
    auth.uid(),
    'attendance',
    'read',
    tenant_id,
    recorded_by,
    jsonb_build_object('resource_id', id, 'student_id', student_id)
  )
);
```

---

## ✅ **التحقق من النجاح**

بعد تطبيق جميع التهجيرات، قم بتشغيل هذه الاستعلامات للتحقق:

```sql
-- فحص الدوال الجديدة
SELECT routine_name 
FROM information_schema.routines 
WHERE routine_schema = 'public' 
  AND routine_name IN ('is_system_admin', 'get_user_role_secure', 'check_permission');

-- فحص مصفوفة الصلاحيات
SELECT COUNT(*) as permission_count 
FROM public.permission_matrix 
WHERE is_active = true;

-- فحص السياسات الجديدة
SELECT tablename, COUNT(*) as policy_count
FROM pg_policies 
WHERE schemaname = 'public'
  AND policyname LIKE '%_policy'
GROUP BY tablename;

-- اختبار دالة فحص الصلاحيات
SELECT public.check_permission(
  auth.uid(),
  'user',
  'read',
  null,
  null,
  '{}'::jsonb
) as can_read_users;
```

---

## 🔧 **استكشاف الأخطاء**

### **إذا ظهرت أخطاء في الدوال:**
```sql
-- حذف الدوال وإعادة إنشائها
DROP FUNCTION IF EXISTS public.is_system_admin(uuid);
DROP FUNCTION IF EXISTS public.get_user_role_secure(uuid);
DROP FUNCTION IF EXISTS public.check_permission(uuid, text, text, uuid, uuid, jsonb);

-- ثم أعد تشغيل كود إنشاء الدوال
```

### **إذا ظهرت أخطاء في السياسات:**
```sql
-- حذف السياسات وإعادة إنشائها
DROP POLICY IF EXISTS "users_read_policy" ON public.users;
DROP POLICY IF EXISTS "users_update_policy" ON public.users;

-- ثم أعد تشغيل كود إنشاء السياسات
```

### **إذا كانت مصفوفة الصلاحيات فارغة:**
```sql
-- حذف الجدول وإعادة إنشائه
DROP TABLE IF EXISTS public.permission_matrix;

-- ثم أعد تشغيل كود إنشاء الجدول والبيانات
```

---

## 🎉 **بعد الانتهاء**

عند اكتمال جميع التهجيرات بنجاح:

1. **اختبر النظام** باستخدام حسابات مختلفة
2. **تأكد من عمل الصلاحيات** بشكل صحيح
3. **راجع سجلات الأخطاء** في Supabase Dashboard
4. **ابدأ في استخدام النظام الجديد** في الكود

**المرحلة الأولى مكتملة! 🚀**
