-- Fix create_student_user function to properly handle auth.users foreign key constraint
-- This function should use the Edge Function approach instead of trying to create auth users directly

DROP FUNCTION IF EXISTS create_student_user(text, text, text, uuid, text);

CREATE OR REPLACE FUNCTION create_student_user(
  user_email text,
  user_password text,
  user_name text,
  user_tenant_id uuid,
  student_grade text
)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
DECLARE
  result json;
BEGIN
  -- This function should not create auth users directly
  -- Instead, it should return an error directing to use the proper Edge Function
  
  result := json_build_object(
    'success', false,
    'error', 'This function is deprecated. Please use the create-user Edge Function instead.',
    'message', 'Student creation should be handled through the proper authentication flow'
  );
  
  RETURN result;
  
EXCEPTION
  WHEN OTHERS THEN
    result := json_build_object(
      'success', false,
      'error', SQLERRM,
      'message', 'Failed to create student'
    );
    RETURN result;
END;
$$;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION create_student_user(text, text, text, uuid, text) TO authenticated;

-- Create a new function that only creates the student record after auth user exists
CREATE OR REPLACE FUNCTION create_student_record(
  auth_user_id uuid,
  student_name text,
  student_grade text,
  student_tenant_id uuid,
  parent_id uuid DEFAULT NULL,
  route_stop_id uuid DEFAULT NULL
)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
DECLARE
  new_student_id uuid;
  result json;
BEGIN
  -- Verify the auth user exists
  IF NOT EXISTS (SELECT 1 FROM auth.users WHERE id = auth_user_id) THEN
    result := json_build_object(
      'success', false,
      'error', 'Auth user does not exist',
      'message', 'Cannot create student record without valid auth user'
    );
    RETURN result;
  END IF;
  
  -- Verify the user record exists in public.users
  IF NOT EXISTS (SELECT 1 FROM users WHERE id = auth_user_id) THEN
    result := json_build_object(
      'success', false,
      'error', 'User profile does not exist',
      'message', 'Cannot create student record without user profile'
    );
    RETURN result;
  END IF;
  
  -- Insert student record
  INSERT INTO students (
    id,
    name,
    grade,
    tenant_id,
    parent_id,
    route_stop_id,
    is_active,
    created_at,
    updated_at
  ) VALUES (
    gen_random_uuid(),
    student_name,
    student_grade,
    student_tenant_id,
    parent_id,
    route_stop_id,
    true,
    now(),
    now()
  ) RETURNING id INTO new_student_id;
  
  -- Return success result
  result := json_build_object(
    'success', true,
    'student_id', new_student_id,
    'user_id', auth_user_id,
    'message', 'Student record created successfully'
  );
  
  RETURN result;
  
EXCEPTION
  WHEN OTHERS THEN
    result := json_build_object(
      'success', false,
      'error', SQLERRM,
      'message', 'Failed to create student record'
    );
    RETURN result;
END;
$$;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION create_student_record(uuid, text, text, uuid, uuid, uuid) TO authenticated;
