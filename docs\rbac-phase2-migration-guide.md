# RBAC Phase 2 Migration Guide

## Overview

This guide outlines the step-by-step process to migrate from the current inconsistent RBAC implementation to a centralized, consistent permission system.

## Current Issues Identified

### Critical Issues
1. **Hardcoded route access control** in `App.tsx`
2. **Inconsistent permission checking** between routing and navigation
3. **Duplicate permission logic** across multiple files
4. **Missing permission-based access control** in several components
5. **Complex hardcoded role filtering** instead of RBAC data scopes

### High Priority Issues
1. **Repeated role checking patterns** across page components
2. **Hardcoded admin checks** in various components
3. **Missing permission checks** for sensitive operations
4. **Mixed permission checking approaches** in navigation
5. **Overlapping validation logic** between frontend and backend

## Migration Steps

### Step 1: Implement Centralized Configuration

```typescript
// ✅ DONE: Created src/lib/rbacCentralizedConfig.ts
// This file contains:
// - ROUTE_PERMISSIONS: Maps routes to required permissions
// - COMPONENT_PERMISSIONS: Maps UI components to permissions
// - DATA_ACCESS_PATTERNS: Defines role-based data access
// - ACTION_PERMISSION_MATRIX: Maps actions to permissions
// - NAVIGATION_CONFIG: Navigation items with permissions
```

### Step 2: Implement Enhanced RBAC Hook

```typescript
// ✅ DONE: Created src/hooks/useRBACEnhanced.ts
// This hook provides:
// - canAccessRoute(): Route-level permission checking
// - canUseComponent(): Component-level permission checking
// - canPerformAction(): Action-based permission checking
// - filterDataByRole(): Role-based data filtering
// - Utility functions for navigation and permissions
```

### Step 3: Create Protected Route Component

```typescript
// ✅ DONE: Created src/components/auth/ProtectedRoute.tsx
// Features:
// - Centralized route protection
// - Consistent error handling
// - Fallback route configuration
// - HOC wrapper for easy integration
```

### Step 4: Enhance Permission Guard Component

```typescript
// ✅ DONE: Created src/components/auth/EnhancedPermissionGuard.tsx
// Improvements:
// - Better error handling
// - Component-based access checking
// - Action-based permission validation
// - Convenience components (AdminOnly, etc.)
```

### Step 5: Migrate App.tsx Routing

**BEFORE (Current Implementation):**
```typescript
// ❌ Hardcoded access control
<Route
  path="/dashboard/schools"
  element={
    AccessControl.canAccessRoute(user, "/dashboard/schools") ? (
      <SchoolsPage />
    ) : (
      <AccessDeniedComponent />
    )
  }
/>
```

**AFTER (Recommended Implementation):**
```typescript
// ✅ Using ProtectedRoute component
<Route
  path="/dashboard/schools"
  element={
    <ProtectedRoute route="/dashboard/schools">
      <SchoolsPage />
    </ProtectedRoute>
  }
/>
```

### Step 6: Migrate Page Components

**BEFORE (SchoolsPage.tsx):**
```typescript
// ❌ Hardcoded role check
{user?.role === UserRole.ADMIN && (
  <Button onClick={() => handleOpenModal()}>
    <Plus size={16} />
    Add School
  </Button>
)}
```

**AFTER (Recommended):**
```typescript
// ✅ Permission-based check
<EnhancedPermissionGuard componentKey="SchoolModal.create">
  <Button onClick={() => handleOpenModal()}>
    <Plus size={16} />
    Add School
  </Button>
</EnhancedPermissionGuard>
```

### Step 7: Migrate Sidebar Navigation

**BEFORE (Sidebar.tsx):**
```typescript
// ❌ Mixed permission checking
if (hasPermission(Permission.VIEW_ALL_SCHOOLS)) {
  items.push({
    to: "/dashboard/schools",
    icon: <School size={20} />,
    label: t("nav.schools"),
    show: true,
  });
}
```

**AFTER (Recommended):**
```typescript
// ✅ Centralized navigation configuration
const navigationItems = rbac.getNavigationItems();
const accessibleItems = navigationItems.filter(item => item.accessible);
```

### Step 8: Migrate Data Filtering

**BEFORE (UsersPage.tsx):**
```typescript
// ❌ Complex hardcoded filtering
const filteredUsers = users.filter((u) => {
  if (user?.role === "admin") {
    return true;
  }
  if (user?.role === "school_manager" || user?.tenant_id) {
    return u.tenant_id === user?.tenant_id;
  }
  return u.id === user?.id;
});
```

**AFTER (Recommended):**
```typescript
// ✅ RBAC-based filtering
const filteredUsers = rbac.filterDataByRole(users, ResourceType.USER, 'id');
```

## Implementation Checklist

### Phase 1: Foundation (Week 1)
- [x] Create centralized RBAC configuration
- [x] Implement enhanced RBAC hook
- [x] Create ProtectedRoute component
- [x] Create EnhancedPermissionGuard component
- [ ] Write comprehensive tests for new components

### Phase 2: Migration (Week 2)
- [ ] Migrate App.tsx routing to use ProtectedRoute
- [ ] Replace hardcoded role checks in SchoolsPage
- [ ] Replace hardcoded role checks in UsersPage
- [ ] Replace hardcoded role checks in BusesPage
- [ ] Replace hardcoded role checks in RoutesPage
- [ ] Replace hardcoded role checks in StudentsPage

### Phase 3: Navigation & Components (Week 3)
- [ ] Migrate Sidebar navigation to use centralized config
- [ ] Replace hardcoded checks in NotificationsPage
- [ ] Add permission checks to ReportsPage
- [ ] Migrate modal components to use EnhancedPermissionGuard
- [ ] Update form components with permission validation

### Phase 4: Backend Alignment (Week 4)
- [ ] Align middleware permission checks with frontend
- [ ] Create shared validation utilities
- [ ] Implement consistent error responses
- [ ] Add comprehensive audit logging

### Phase 5: Testing & Documentation (Week 5)
- [ ] Write unit tests for all RBAC components
- [ ] Write integration tests for permission flows
- [ ] Update component documentation
- [ ] Create permission testing guide
- [ ] Performance testing and optimization

## Code Examples

### Using the Enhanced Permission Guard

```typescript
// Simple permission check
<EnhancedPermissionGuard permission={Permission.USERS_CREATE}>
  <CreateUserButton />
</EnhancedPermissionGuard>

// Multiple permissions (any)
<EnhancedPermissionGuard 
  permissions={[Permission.USERS_UPDATE_ALL, Permission.USERS_UPDATE_TENANT]}
>
  <EditUserButton />
</EnhancedPermissionGuard>

// Role-based access
<EnhancedPermissionGuard roles={[UserRole.ADMIN, UserRole.SCHOOL_MANAGER]}>
  <AdminPanel />
</EnhancedPermissionGuard>

// Component-based access
<EnhancedPermissionGuard componentKey="UserModal.create">
  <CreateUserModal />
</EnhancedPermissionGuard>

// Action-based access
<EnhancedPermissionGuard 
  resource={ResourceType.BUS} 
  action={Action.DELETE}
  context={{ resourceOwnerId: bus.driver_id }}
>
  <DeleteBusButton />
</EnhancedPermissionGuard>
```

### Using the Enhanced RBAC Hook

```typescript
function MyComponent() {
  const rbac = useRBACEnhanced();
  
  // Check route access
  const canAccessReports = rbac.canAccessRoute('/dashboard/reports');
  
  // Check component access
  const canCreateUser = rbac.canUseComponent('UserModal.create');
  
  // Check action permission
  const canDeleteBus = rbac.canPerformAction(
    ResourceType.BUS, 
    Action.DELETE, 
    { resourceOwnerId: bus.driver_id }
  );
  
  // Filter data by role
  const accessibleUsers = rbac.filterDataByRole(allUsers, ResourceType.USER);
  
  // Get accessible navigation items
  const navItems = rbac.getNavigationItems();
  
  return (
    <div>
      {canAccessReports.allowed && <ReportsLink />}
      {canCreateUser.allowed && <CreateUserButton />}
      {canDeleteBus.allowed && <DeleteBusButton />}
    </div>
  );
}
```

## Testing Strategy

### Unit Tests
- Test each RBAC utility function
- Test permission guard components with different props
- Test protected route component with various scenarios
- Test enhanced RBAC hook with different user roles

### Integration Tests
- Test complete permission flows from login to action
- Test navigation accessibility based on roles
- Test data filtering across different components
- Test error handling and fallback routes

### Performance Tests
- Measure permission checking performance
- Test with large datasets and complex permission matrices
- Optimize caching and memoization

## Rollback Plan

If issues arise during migration:

1. **Immediate Rollback**: Keep old components alongside new ones
2. **Feature Flags**: Use environment variables to toggle between old/new systems
3. **Gradual Migration**: Migrate one page/component at a time
4. **Monitoring**: Add extensive logging to track permission checks

## Success Metrics

- **Code Reduction**: 50% reduction in permission-related code duplication
- **Consistency**: 100% of permission checks use centralized system
- **Maintainability**: New permissions can be added without code changes
- **Performance**: No degradation in page load times
- **Security**: Zero permission bypass vulnerabilities

## Next Steps

1. Review and approve this migration plan
2. Set up development branch for RBAC migration
3. Begin Phase 1 implementation
4. Schedule regular review meetings
5. Plan user acceptance testing

---

**Note**: This migration should be done incrementally to minimize risk. Each phase should be thoroughly tested before proceeding to the next.
